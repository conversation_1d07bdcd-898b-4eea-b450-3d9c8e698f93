# 奖励中台后端技术规格书 (OTS最终版)

## 1. 系统概述

本文档为一份完整的后端技术规格说明书，旨在为开发一个功能全面、高内聚、低耦合的**奖励中台**提供清晰、详尽的实现指南。该系统**专为阿里云表格存储（OTS）设计**，作为平台能力，支持由多个外部业务渠道驱动，并集成了动态奖池、多维度风控、多种激励机制和灵活的概率策略。

### 核心功能：

- **概率性抽奖**：
  - **动态奖池**：支持根据用户存储的、与奖池挂钩的偏好（如选择的英雄），动态替换奖池中的专属奖品。
  - **双重概率策略**：支持“整体概率 (OVERALL)”和“单品概率 (SINGLE)”两种模式。
  - **两阶段抽奖**：提供独立的“资产兑换抽奖券”和“消耗抽奖券批量抽奖”接口，由前端编排调用。
- **组合奖励 (礼包/宝箱)**：支持配置“礼包”型奖品，内含多个固定奖励和随机奖励。可通过抽奖获得，也可由外部服务直接按ID触发发放。
- **统一领奖能力**：提供一个统一、抽象的领奖接口，用于核销由不同业务系统（如抽奖进度、任务系统）为用户生成的“领奖凭证”。
- **定向奖励发放**：提供安全的内部API，允许其他微服务直接指定奖励内容并调用本中台进行发放。
- **多维度抽奖风控**：可按日、周、月三个维度精确控制用户的抽奖次数上限。
- **后台管理支持**：提供独立的API接口，供管理后台配置活动和监控库存。

## 2. 系统架构与流程图

### 2.1 业务流程图

#### **A. 抽奖与进度奖励流程**

```
[游戏客户端]
    |
    | 1. 调用批量抽奖接口
    v
[奖励中台 API (/lottery/draw-batch)]
    |
    | 2. (内部) 执行抽奖 & 更新用户进度 (写入 user_progress_tracker)
    |
    | 3. (判断) 用户进度是否达到解锁宝箱条件？
    |    |
    |    +-----> 是 ----> [奖励中台 核心逻辑]
    |                       |
    |                       | 4. (内部调用) 创建一条“领奖凭证” (含 sceneCode)
    |                       v
    |                    [user_claim_entitlement 表]
    v
[游戏客户端] <--- (轮询/推送) --- [奖励中台]
    |
    | 5. 发现有新的可领取奖励，根据 sceneCode 在对应页面显示“领取”按钮
    v
[奖励中台 API (/rewards/claim)]
    |
    | 6. 接收领奖请求 (含 claimId)
    v
[奖励中台 核心逻辑]
    |
    | 7. 核销凭证，发放宝箱奖励，更新凭证状态
    v
[数据库]
```

#### **B. 外部服务授予奖励流程**

```
[上游服务 (如任务系统)]
    |
    | 1. 业务完成，决定授予用户奖励
    v
[奖励中台 内部API (grantClaimEntitlement)]
    |
    | 2. 创建一条“领奖凭证” (含 sceneCode)
    v
[user_claim_entitlement 表]
    |
    | 3. (后续流程同上)
    v
[游戏客户端] -> [奖励中台 API (/rewards/claim)] -> ...
```

## 3. 数据模型 (OTS 表设计)

**注意**: 所有与租户相关的配置表，主键均以 `saas_id` 作为**分区键**，以确保数据隔离和性能。

### 3.1 `item_master_config` - 物品主数据表

| **字段**           | **类型**             | **含义**                                                     |
| ------------------ | -------------------- | ------------------------------------------------------------ |
| `saas_id`          | STRING               | **主键1 (分区键)**: 对接的应用/租户ID                        |
| `item_id`          | STRING               | **主键2**: **系统内部**的物品唯一ID (如 `health_potion_s`, `gold_currency`) |
| `item_name`        | STRING               | 物品的官方名称 (如 “小型生命药水”, “金币”)                   |
| `item_icon`        | STRING               | 物品的官方图标URL                                            |
| `item_type`        | STRING               | 物品的类型 (`ITEM`, `CURRENCY`, `GIFT_PACK`, `AVATAR_FRAME`等) |
| `external_item_id` | STRING               | **游戏/外部系统的道具ID** (`NULL`表示非实体道具)             |
| `description`      | STRING               | 物品的描述                                                   |
| `is_active`        | BOOLEAN              | 该物品是否在整个系统中可用                                   |
| `create_time`      | BIGINT (Timestamp)   | 创建时间                                                     |
| `update_time`      | BIGINT (Timestamp)   | 更新时间                                                     |
| **PK**             | `(saas_id, item_id)` | 复合主键                                                     |

### 3.2 `prize_pool` - 奖池配置表

| **字段**                   | **类型**           | **含义**                              |
| -------------------------- | ------------------ | ------------------------------------- |
| `saas_id`                  | STRING             | **主键1 (分区键)**: 对接的应用/租户ID |
| `code`                     | STRING             | **主键2**: 奖池唯一编码               |
| `name`                     | STRING             | 奖池名称                              |
| `exchange_rules`           | STRING (JSON)      | 兑换规则                              |
| `probability_strategy`     | STRING             | 概率策略 (`OVERALL` 或 `SINGLE`)      |
| `fallback_prize_config_id` | STRING             | 兜底奖品配置ID (`SINGLE`策略专用)     |
| `daily_limit`              | BIGINT             | 每日抽奖上限 (-1表示无限制)           |
| `weekly_limit`             | BIGINT             | 每周抽奖上限 (-1表示无限制)           |
| `monthly_limit`            | BIGINT             | 每月抽奖上限 (-1表示无限制)           |
| `chest_cycle_days`         | BIGINT             | 进度宝箱的刷新周期（天）              |
| `status`                   | STRING             | 状态 (`ACTIVE`, `INACTIVE`)           |
| `start_time`               | BIGINT (Timestamp) | 活动开始时间                          |
| `end_time`                 | BIGINT (Timestamp) | 活动结束时间                          |
| `create_time`              | BIGINT (Timestamp) | 创建时间                              |
| `update_time`              | BIGINT (Timestamp) | 更新时间                              |
| **PK**                     | `(saas_id, code)`  | 复合主键                              |

### 3.3 `prize_config` - 奖品配置表

| **字段**              | **类型**                                | **含义**                                                   |
| --------------------- | --------------------------------------- | ---------------------------------------------------------- |
| `saas_id`             | STRING                                  | **主键1 (分区键)**: 对接的应用/租户ID                      |
| `prize_pool_code`     | STRING                                  | **主键2**: 所属奖池编码                                    |
| `config_id`           | STRING                                  | **主键3**: 配置规则的唯一ID (UUID)                         |
| `preference_type`     | STRING                                  | 关联的偏好类型 (`NULL`表示通用)                            |
| `preference_value`    | STRING                                  | 关联的偏好值                                               |
| `reward_name`         | STRING                                  | **冗余**: 奖品名称（用于展示）                             |
| `reward_icon`         | STRING                                  | **冗余**: 奖品图标URL（用于展示）                          |
| `reward_type`         | STRING                                  | **冗余**: 奖励类型 (`ITEM`, `CURRENCY`, `GIFT_PACK`)       |
| `reward_item_id`      | STRING                                  | **引用**: 奖励的业务ID (关联 `item_master_config.item_id`) |
| `reward_quantity`     | BIGINT                                  | **规则**: 奖励数量                                         |
| `winning_probability` | DOUBLE                                  | 中奖概率                                                   |
| `stock_quantity`      | BIGINT                                  | 库存数量 (-1 表示无限)                                     |
| `is_active`           | BOOLEAN                                 | 是否生效                                                   |
| `create_time`         | BIGINT (Timestamp)                      | 创建时间                                                   |
| `update_time`         | BIGINT (Timestamp)                      | 更新时间                                                   |
| **PK**                | `(saas_id, prize_pool_code, config_id)` | 复合主键                                                   |

### 3.4 `user_lottery_profile` - 用户抽奖档案表

| **字段**             | **类型**           | **含义**                        |
| -------------------- | ------------------ | ------------------------------- |
| `user_id`            | STRING             | **主键1 (分区键)**: 用户ID      |
| `total_draw_count`   | BIGINT             | 历史总抽奖次数                  |
| `daily_draw_count`   | BIGINT             | 今日已抽奖次数 **(仅用于风控)** |
| `weekly_draw_count`  | BIGINT             | 本周已抽奖次数 **(仅用于风控)** |
| `monthly_draw_count` | BIGINT             | 本月已抽奖次数 **(仅用于风控)** |
| `last_draw_time`     | BIGINT (Timestamp) | 上次抽奖时间                    |
| `create_time`        | BIGINT (Timestamp) | 创建时间                        |
| `update_time`        | BIGINT (Timestamp) | 更新时间                        |
| **PK**               | `(user_id)`        | 主键                            |

### 3.5 `progress_chest_config` - 进度宝箱配置表

| **字段**            | **类型**                                | **含义**                              |
| ------------------- | --------------------------------------- | ------------------------------------- |
| `saas_id`           | STRING                                  | **主键1 (分区键)**: 对接的应用/租户ID |
| `prize_pool_code`   | STRING                                  | **主键2**: 关联的奖池编码             |
| `config_id`         | STRING                                  | **主键3**: 宝箱配置的唯一ID (UUID)    |
| `preference_type`   | STRING                                  | 关联的偏好类型 (`NULL`表示通用)       |
| `preference_value`  | STRING                                  | 关联的偏好值                          |
| `scene_code`        | STRING                                  | **场景编码 (用于前端展示定位)**       |
| `chest_name`        | STRING                                  | 宝箱名称                              |
| `unlock_progress`   | BIGINT                                  | 解锁所需的进度值                      |
| `pack_id_on_unlock` | STRING                                  | 解锁时对应的礼包ID                    |
| `display_order`     | BIGINT                                  | 显示顺序                              |
| `is_active`         | BOOLEAN                                 | 是否启用                              |
| `create_time`       | BIGINT (Timestamp)                      | 创建时间                              |
| `update_time`       | BIGINT (Timestamp)                      | 更新时间                              |
| **PK**              | `(saas_id, prize_pool_code, config_id)` | 复合主键                              |

### 3.6 `user_claim_entitlement` - 用户领奖凭证表

| **字段**                | **类型**              | **含义**                                    |
| ----------------------- | --------------------- | ------------------------------------------- |
| `user_id`               | STRING                | **主键1 (分区键)**: 用户ID                  |
| `claim_id`              | STRING                | **主键2**: 领奖凭证唯一ID (UUID)            |
| `scene_code`            | STRING                | **场景编码 (用于前端展示定位)**             |
| `reward_type`           | STRING                | 奖励类型 (`PROGRESS_CHEST`, `GRANTED_PACK`) |
| `reward_source_id`      | STRING                | 奖励来源ID (如 `chestConfigId` 或 `packId`) |
| `status`                | STRING                | 状态 (`UNCLAIMED`, `CLAIMED`)               |
| `source_channel`        | STRING                | 凭证来源渠道                                |
| `source_transaction_id` | STRING                | 来源渠道的唯一交易ID (幂等键)               |
| `create_time`           | BIGINT (Timestamp)    | 创建时间                                    |
| `update_time`           | BIGINT (Timestamp)    | 更新时间                                    |
| **PK**                  | `(user_id, claim_id)` | 复合主键                                    |

### 3.7 `user_preference` - 用户偏好表

| **字段**           | **类型**                                      | **含义**                                 |
| ------------------ | --------------------------------------------- | ---------------------------------------- |
| `user_id`          | STRING                                        | **主键1 (分区键)**: 用户ID               |
| `prize_pool_code`  | STRING                                        | **主键2**: 关联的奖池编码                |
| `preference_type`  | STRING                                        | **主键3**: 偏好类型 (如 `SELECTED_HERO`) |
| `preference_value` | STRING                                        | 偏好值 (如 `HERO_A_001`)                 |
| `update_time`      | BIGINT (Timestamp)                            | 更新时间                                 |
| **PK**             | `(user_id, prize_pool_code, preference_type)` | 复合主键                                 |

### 3.8 `draw_history` - 抽奖历史记录表

| **字段**               | **类型**              | **含义**                         |
| ---------------------- | --------------------- | -------------------------------- |
| `user_id`              | STRING                | **主键1 (分区键)**: 用户ID       |
| `event_id`             | STRING                | **主键2**: 抽奖事件唯一ID (UUID) |
| `batch_transaction_id` | STRING                | 批量抽奖的唯一交易ID             |
| `prize_pool_code`      | STRING                | 抽奖时所在的奖池                 |
| `prize_config_id`      | STRING                | 中奖的奖品配置ID                 |
| `reward_name`          | STRING                | 中奖的奖品名称                   |
| `draw_time`            | BIGINT (Timestamp)    | 抽奖时间                         |
| **PK**                 | `(user_id, event_id)` | 复合主键                         |

### 3.9 `gift_pack_config` - 礼包内容规则表

| **字段**         | **类型**                      | **含义**                                             |
| ---------------- | ----------------------------- | ---------------------------------------------------- |
| `saas_id`        | STRING                        | **主键1 (分区键)**: 对接的应用/租户ID                |
| `pack_id`        | STRING                        | **主键2**: 礼包ID                                    |
| `rule_id`        | STRING                        | **主键3**: 规则的唯一ID (UUID)                       |
| `rule_type`      | STRING                        | 规则类型 (`FIXED_ITEM` 或 `RANDOM_POOL_PICK`)        |
| `item_id`        | STRING                        | **引用**: 物品ID (关联 `item_master_config.item_id`) |
| `quantity_min`   | BIGINT                        | **规则**: 最小数量                                   |
| `quantity_max`   | BIGINT                        | **规则**: 最大数量                                   |
| `random_pool_id` | STRING                        | 随机池ID                                             |
| `pick_count`     | BIGINT                        | 从随机池中抽取的数量                                 |
| **PK**           | `(saas_id, pack_id, rule_id)` | 复合主键                                             |

### 3.10 `random_reward_pool` - 随机奖励池定义表

| **字段**       | **类型**                      | **含义**                                                     |
| -------------- | ----------------------------- | ------------------------------------------------------------ |
| `saas_id`      | STRING                        | **主键1 (分区键)**: 对接的应用/租户ID                        |
| `pool_id`      | STRING                        | **主键2**: 随机池ID                                          |
| `item_id`      | STRING                        | **主键3**: **引用**: 池内包含的物品ID (关联 `item_master_config.item_id`) |
| `quantity_min` | BIGINT                        | **规则**: 抽中该物品时获得的最小数量                         |
| `quantity_max` | BIGINT                        | **规则**: 抽中该物品时获得的最大数量                         |
| `weight`       | BIGINT                        | 抽中该物品的权重                                             |
| **PK**         | `(saas_id, pool_id, item_id)` | 复合主键                                                     |

### 3.11 `direct_reward_issuance_log` - 定向奖励发放流水表

| **字段**          | **类型**           | **含义**                                          |
| ----------------- | ------------------ | ------------------------------------------------- |
| `transaction_id`  | STRING             | **主键1 (分区键)**: 外部渠道的唯一交易ID (幂等键) |
| `user_id`         | STRING             | 目标用户ID                                        |
| `rewards_content` | STRING (JSON)      | 发放的奖励内容                                    |
| `channel`         | STRING             | 来源渠道                                          |
| `scenario_code`   | STRING             | 业务场景编码                                      |
| `create_time`     | BIGINT (Timestamp) | 创建时间                                          |
| **PK**            | `(transaction_id)` | 主键                                              |

### 3.12 `user_progress_tracker` - 用户进度跟踪表

| **字段**           | **类型**                     | **含义**                   |
| ------------------ | ---------------------------- | -------------------------- |
| `user_id`          | STRING                       | **主键1 (分区键)**: 用户ID |
| `prize_pool_code`  | STRING                       | **主键2**: 关联的奖池编码  |
| `current_progress` | BIGINT                       | 当前进度值                 |
| `cycle_start_time` | BIGINT (Timestamp)           | 当前周期的开始时间         |
| `update_time`      | BIGINT (Timestamp)           | 更新时间                   |
| **PK**             | `(user_id, prize_pool_code)` | 复合主键                   |

## 4. 核心业务逻辑

### 4.1 发奖策略

当抽奖命中、礼包开启或直接发放时，系统根据奖励规则中的 `reward_item_id` 和 `reward_quantity` 执行发奖流程：

1. **查询主数据**: 使用 `reward_item_id` 查询 `item_master_config` 表，获取该物品的 `item_type` 和 `external_item_id`。

2. 选择策略: 根据查询到的 item_type，执行不同的发奖策略。

   | item_type | 业务含义       | 后端执行流程 |

   | :--- | :--- | :--- |

   | ITEM | 游戏内实体道具 | 调用游戏服的“添加道具”接口，传递 external_item_id 和 reward_quantity。 |

   | CURRENCY | 游戏内货币 | 调用资产中心的“增加货币”接口，传递 item_id (如 "GOLD") 和 reward_quantity。 |

   | GIFT_PACK | 礼包 | 调用奖励中台自身的“开启礼包”逻辑，传递 reward_item_id (即 pack_id)。 |

   | ... | ... | ... |

### 4.2 批量抽奖与进度处理

1. **抽奖主流程**：
   - **获取用户偏好**：在构建奖池前，通过内部接口并**指定当前 `prizePoolCode`** 查询 `user_preference` 表，获取用户针对该奖池的偏好设置。
   - **构建动态奖池**：筛选 `prize_config` 表时，保留所有 `preference_type` 为 `NULL` 的通用奖品，并根据用户的偏好，筛选出匹配的专属奖品。
2. **更新用户进度**：
   - 在批量抽奖成功后，读取奖池配置中的 `chest_cycle_days`。
   - 查询 `user_progress_tracker` 表中该用户和奖池的记录。
   - **判断周期是否过期**：比较记录中的 `cycle_start_time` 和当前时间。
     - **如果已过期**：将 `current_progress` 重置为本次的抽奖次数，并更新 `cycle_start_time` 为当前时间。
     - **如果未过期**：将 `current_progress` 累加上本次的抽奖次数。
   - 将更新后的进度写回 `user_progress_tracker` 表。
3. **检查并生成凭证**：检查 `user_progress_tracker` 中更新后的 `current_progress` 值是否跨越了宝箱配置的阈值（同样需要根据用户偏好筛选出对应的宝箱配置）。对于新解锁的宝箱，**获取其配置的`scene_code`**，然后调用内部服务 `grantClaimEntitlement`，在 `user_claim_entitlement` 表中**创建一条新的凭证记录**。

### 4.3 缓存策略

- **配置缓存**: `prize_pool`, `prize_config` 等不常变动的配置数据，在服务启动时或管理员更新配置后加载到Redis中。
- **库存缓存**: 有限库存奖品的 `stock_quantity` 必须在Redis中进行实时管理，使用 `DECRBY` 等原子操作扣减库存，并异步同步回数据库。
- **用户数据缓存**: `user_lottery_profile`, `user_progress_tracker` 等用户频繁读写的数据可被缓存，以提升性能。

## 5. API 接口设计

### 5.1 用户端 API

#### `POST /api/lottery/exchange-tickets`

用户发起一次资产兑换抽奖券的操作。

- **Request Body**:

  ```
  {
    "prizePoolCode": "NEWBIE_POOL",
    "exchangeType": "POINTS_TO_TICKET_WITH_ALLIANCE_TRANSFER",
    "assetType": "POINTS",
    "optionId": "OPTION_5_TICKETS"
  }
  ```

- **Success Response (200 OK)**:

  ```
  {
    "success": true,
    "data": {
      "ticketsObtained": 5
    }
  }
  ```

#### `POST /api/lottery/draw-batch`

用户消耗抽奖券进行批量抽奖。

- **Request Body**:

  ```
  {
    "prizePoolCode": "NEWBIE_POOL",
    "drawCount": 10
  }
  ```

- **Success Response (200 OK)**:

  ```
  {
    "success": true,
    "data": {
      "batchTransactionId": "draw-batch-987654321",
      "totalRewards": [
        { "rewardItemId": "gold_currency", "rewardName": "金币", "quantity": 1500, "rewardIcon": "url_to_gold_icon" },
        { "rewardItemId": "exp_potion_s", "rewardName": "小型经验药水", "quantity": 8, "rewardIcon": "url_to_potion_icon" }
      ]
    }
  }
  ```

#### `POST /api/lottery/select-preference`

用户设置与奖池挂钩的偏好。

- **Request Body**:

  ```
  {
    "prizePoolCode": "FACTION_WAR_POOL",
    "preferenceType": "SELECTED_FACTION",
    "preferenceValue": "LIGHT_SIDE"
  }
  ```

- **Success Response (200 OK)**:

  ```
  {
    "success": true,
    "message": "偏好设置成功"
  }
  ```

#### `GET /api/lottery/state`

获取用户在抽奖活动中的所有状态。

- **Request Parameters**: `prizePoolCode`

- **Success Response (200 OK)**:

  ```
  {
    "success": true,
    "data": {
      "prizePool": {
        "code": "NEWBIE_POOL",
        "name": "新手奖池",
        "exchangeRules": [
          {
            "exchangeType": "POINTS_TO_TICKET_WITH_ALLIANCE_TRANSFER",
            "assetType": "POINTS",
            "options": [
              {
                "optionId": "OPTION_1_TICKET",
                "cost": 1200,
                "tickets": 1,
                "description": "1200积分抽1次"
              }
            ]
          }
        ],
        "prizes": [
          { "rewardName": "金币", "rewardIcon": "url_to_gold_icon" },
          { "rewardName": "英雄A皮肤", "rewardIcon": "url_to_skin_icon" }
        ]
      },
      "userProfile": {
        "preferences": {
          "SELECTED_HERO": "HERO_A_001"
        },
        "progress": {
          "currentValue": 15,
          "cycleDurationDays": 7,
          "cycleEndTime": "2025-09-07T23:59:59Z"
        },
        "drawLimits": {
          "daily": { "used": 5, "limit": 10 },
          "weekly": { "used": 15, "limit": 50 },
          "monthly": { "used": 15, "limit": -1 }
        }
      },
      "chests": [
        {
          "chestId": "chest-config-uuid-1",
          "chestName": "青铜宝箱",
          "chestIcon": "url_to_bronze_chest",
          "unlockProgress": 20,
          "state": "LOCKED",
          "claimId": null
        }
      ],
      "claimableRewards": [
        {
          "claimId": "b8c2d4e5-f6a7-8901-2345-67890abcdef1",
          "sceneCode": "DAILY_TASKS",
          "rewardType": "GRANTED_PACK",
          "rewardName": "任务奖励礼包",
          "rewardIcon": "url_to_pack_icon"
        }
      ]
    }
  }
  ```

#### `POST /api/rewards/claim`

用户领取一个已解锁的奖励。

- **Request Body**:

  ```
  {
    "claimId": "a-unique-uuid-representing-the-entitlement"
  }
  ```

- **Success Response (200 OK)**:

  ```
  {
    "success": true,
    "data": {
      "rewards": [
        { "rewardItemId": "gold_currency", "rewardName": "金币", "quantity": 2000, "rewardIcon": "url_to_gold_icon" }
      ]
    }
  }
  ```

### 5.2 管理后台 API

#### `POST /api/admin/pools`

创建或更新一个奖池配置。

#### `POST /api/admin/prizes`

创建或更新奖池内的奖品。

#### `GET /api/admin/stock/realtime`

实时查看奖品库存（直接从Redis读取）。

#### `POST /api/admin/cache/refresh`

手动触发系统重载所有配置到缓存中。

### 5.3 内部服务 API (Dubbo)

#### `setUserPreference(SetUserPreferenceRequest request)`

- **Request DTO**:

  ```
  public class SetUserPreferenceRequest implements Serializable {
      private String userId;
      private String saasId;
      private String prizePoolCode; // 必填
      private String preferenceType;
      private String preferenceValue;
  }
  ```

#### `getUserPreference(GetUserPreferenceRequest request)`

- **Request DTO**:

  ```
  public class GetUserPreferenceRequest implements Serializable {
      private String userId;
      private String saasId;
      private String prizePoolCode; // 必填
      private String preferenceType;
  }
  ```

#### `grantClaimEntitlement(GrantEntitlementRequest request)`

由上游服务（如任务系统）为用户创建领奖凭证。

- **Request DTO**:

  ```
  public class GrantEntitlementRequest implements Serializable {
      private String userId;
      private String saasId;
      private String sceneCode; // 前端展示场景
      private String rewardType;
      private String rewardSourceId;
      private String sourceChannel;
      private String sourceTransactionId; // 用于幂等性
  }
  ```

#### `issuePack(IssuePackRequest request)`

由外部业务系统调用，按ID为用户发放一个宝箱/礼包（直接发放，无需用户领取）。

- **Request DTO**:

  ```
  public class IssuePackRequest implements Serializable {
      private String userId;
      private String saasId;
      private String packId;
      private String channel;
      private String scenarioCode;
      private String transactionId;
      private String description;
  }
  ```

#### `issueDirect(IssueDirectRequest request)`

由外部业务系统调用，直接为用户发放一组指定的奖励。

- **Request DTO**:

  ```
  public class IssueDirectRequest implements Serializable {
      private String userId;
      private String saasId;
      private List<RewardItem> rewards; // RewardItem: { itemId, quantity }
      private String channel;
      private String scenarioCode;
      private String transactionId;
      private String description;
  }
  ```