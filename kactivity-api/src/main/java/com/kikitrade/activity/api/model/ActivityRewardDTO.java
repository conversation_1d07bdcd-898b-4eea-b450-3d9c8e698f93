package com.kikitrade.activity.api.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ActivityRewardDTO implements Serializable {

    private String batchId;

    private String customerId;

    private String seq;

    private String status;

    private String activityId;

    private String userName;

    private String nickName;

    private String currency;

    private String amount;

    private Date created;

    private Date modified;

    private String phone;

    private String email;

    private Date rewardTime;

    private Integer shard;

    private String message;

    private String businessId;

    private String rewardType;

    private String side;

    private String userType = "Normal";

    private String scope;

    private String level;

    private String referId;

    private String activityName;

    // 默认是USD币种
    private BigDecimal cost;

    private String businessType;

    private String extendParam;

    private String saasId;

    private String inviteeId;
}
