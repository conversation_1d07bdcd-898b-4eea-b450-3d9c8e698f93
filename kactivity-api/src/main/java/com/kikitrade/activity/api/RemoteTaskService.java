package com.kikitrade.activity.api;

import com.kikitrade.activity.api.model.ActivityEventMessageDTO;
import com.kikitrade.activity.api.model.request.TaskListRequest;
import com.kikitrade.activity.api.model.response.*;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.response.ActivityResponse;

import java.util.List;

public interface RemoteTaskService {

    /**
     * 任务列表
     * @param request
     * @return
     */
    List<TaskListResponse> taskList(TaskListRequest request);

    /**
     * 任务详情
     * @param taskId
     * @return
     */
    TaskDetailResponse getTask(String taskId, String customerId);

    /**
     * 根据 code 查询任务详情
     * @param taskCode
     * @param customerId
     * @return
     */
    TaskCodeDetailResponse getTaskByCode(String saasId, String taskCode, String customerId);

    /**
     * 根据 code 查询应得奖励
     * @param saasId
     * @param taskCode
     * @param vipLevelEnum
     * @return
     */
    List<Award> getTaskRewardByCode(String saasId, String taskCode, ActivityConstant.VipLevelEnum vipLevelEnum);

    List<Award> listTaskRewards(String saasId, String taskCode, ActivityConstant.VipLevelEnum vipLevelEnum);

    /**
     * 获取任务进度
     * @param saasId
     * @param taskId
     * @param customerId
     * @return
     */
    TaskProgressResponse getTaskProgress(String saasId, String taskId, String customerId, String type);

    /**
     * 获取任务状态
     * @param saasId
     * @param taskId
     * @param customerId
     * @return
     */
    TaskCompletedResult getTaskStatus(String saasId, String taskId, String customerId);

    /**
     * 获取任务状态
     * @param saasId
     * @param taskCode
     * @param customerId
     * @return
     */
    TaskCompletedResult getTaskStatusByCode(String saasId, String taskCode, String customerId);

    /**
     * 做任务
     * @param activityEventMessageDTO
     * @return
     */
    ActivityResponse<List<Award>> task(ActivityEventMessageDTO activityEventMessageDTO);

    /**
     * 获取任务状态
     * @param saasId
     * @param taskId
     * @param targetId
     * @return
     */
    TaskCompletedResult getTaskStatusByTargetId(String saasId, String taskId, String targetId);
}
