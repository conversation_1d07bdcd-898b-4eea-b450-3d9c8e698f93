package com.kikitrade.activity.api.model.response.reward;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 获取可领取奖励列表响应
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
public class GetClaimableRewardsResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 可领取奖励列表
     */
    private List<ClaimableRewardInfo> rewards;

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 可领取奖励信息
     */
    @Data
    @Builder
    public static class ClaimableRewardInfo implements Serializable {
        
        /**
         * 领奖凭证ID
         */
        private String claimId;
        
        /**
         * 场景编码 (用于前端展示定位)
         */
        private String sceneCode;
        
        /**
         * 奖励类型
         */
        private String rewardType;
        
        /**
         * 奖励名称
         */
        private String rewardName;
        
        /**
         * 奖励图标URL
         */
        private String rewardIcon;
        
        /**
         * 奖励描述
         */
        private String rewardDescription;
        
        /**
         * 创建时间
         */
        private Long createTime;
        
        /**
         * 过期时间
         */
        private Long expireTime;
        
        /**
         * 是否即将过期（24小时内）
         */
        private Boolean expiringSoon;
    }
}
