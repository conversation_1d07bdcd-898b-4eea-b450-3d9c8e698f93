package com.kikitrade.activity.api;

import com.kikitrade.activity.api.model.ActivityRewardDTO;
import com.kikitrade.activity.api.model.request.reward.ActivityRewardRequest;
import com.kikitrade.activity.model.response.ActivityResponse;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/27 17:46
 */
public interface RemoteRewardService {

    /**
     * 领取奖励
     * @param customerId
     * @param taskId
     * @return
     * @throws Exception
     */
    ActivityResponse receiveReward(String customerId, String taskId) throws Exception;

    /**
     * 查询待领取的奖励
     * @param activityRewardRequest
     * @return
     */
    ActivityRewardDTO findReward(ActivityRewardRequest activityRewardRequest);
}
