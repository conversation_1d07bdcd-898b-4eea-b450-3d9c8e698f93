package com.kikitrade.activity.api.model.response;

import com.kikitrade.activity.model.domain.GroupAward;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/21 19:24
 */
@Data
public class TaskDetailResponse implements Serializable {

    private String taskId;
    private String title;
    private String desc;
    /**
     * 任务图片
     */
    private Map<String, String> image;
    private String labelName;
    private String labelColor;
    private Long startTime;
    private Long endTime;
    private int process;
    private int total;
    private int status;
    private String saasId;
    private String platform;
    private GroupAward reward;
    private List<TaskVO> subTasks;

    private int rewardStatus;
}
