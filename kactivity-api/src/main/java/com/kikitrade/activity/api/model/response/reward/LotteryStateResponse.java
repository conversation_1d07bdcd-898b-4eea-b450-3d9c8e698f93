package com.kikitrade.activity.api.model.response.reward;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 获取用户抽奖状态响应
 * 返回用户在抽奖活动中的完整状态信息
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
public class LotteryStateResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 数据内容
     */
    private LotteryStateData data;

    @Data
    @Builder
    public static class PrizePoolInfo implements Serializable {
        /**
         * 奖池编码
         */
        private String code;

        /**
         * 奖池名称
         */
        private String name;

        /**
         * 兑换规则
         */
        private List<ExchangeRule> exchangeRules;

        /**
         * 奖品列表
         */
        private List<PrizeInfo> prizes;
    }

    @Data
    @Builder
    public static class LotteryStateData implements Serializable {
        /**
         * 奖池信息
         */
        private PrizePoolInfo prizePool;

        /**
         * 用户信息
         */
        private UserProfileInfo userProfile;

        /**
         * 宝箱列表
         */
        private List<ChestInfo> chests;

        /**
         * 可领取奖励列表
         */
        private List<ClaimableRewardInfo> claimableRewards;
    }

    @Data
    @Builder
    public static class ExchangeRule implements Serializable {
        /**
         * 兑换类型
         */
        private String exchangeType;

        /**
         * 资产类型
         */
        private String assetType;

        /**
         * 兑换选项列表
         */
        private List<ExchangeOption> options;
    }

    @Data
    @Builder
    public static class ExchangeOption implements Serializable {
        /**
         * 选项ID
         */
        private String optionId;

        /**
         * 消耗数量
         */
        private Integer cost;

        /**
         * 获得抽奖券数量
         */
        private Integer tickets;

        /**
         * 描述
         */
        private String description;
    }

    @Data
    @Builder
    public static class PrizeInfo implements Serializable {
        /**
         * 奖品ID
         */
        private String rewardId;

        /**
         * 奖品名称
         */
        private String rewardName;

        /**
         * 奖品图标
         */
        private String rewardIcon;

        /**
         * 奖品数量
         */
        private Integer quantity;
    }

    @Data
    @Builder
    public static class UserProfileInfo implements Serializable {
        /**
         * 用户偏好设置
         */
        private Map<String,String> preferences;

        /**
         * 用户进度信息
         */
        private ProgressInfo progress;

        /**
         * 抽奖限制信息
         */
        private DrawLimits drawLimits;
    }



    @Data
    @Builder
    public static class ProgressInfo implements Serializable {
        /**
         * 周期开始时间
         */
        private Long cycleStartTime;
        /**
         * 周期天数
         */
        private String cycleDurationDays;

        /**
         * 当前值
         */
        private Integer currentValue;

        /**
         * 周期结束时间
         */
        private Long cycleEndTime;
    }

    @Data
    @Builder
    public static class DrawLimits implements Serializable {
        /**
         * 每日限制
         */
        private LimitInfo daily;

        /**
         * 每周限制
         */
        private LimitInfo weekly;

        /**
         * 每月限制
         */
        private LimitInfo monthly;
    }

    @Data
    @Builder
    public static class LimitInfo implements Serializable {
        /**
         * 已使用次数
         */
        private Integer used;

        /**
         * 限制次数
         */
        private Integer limit;
    }

    @Data
    @Builder
    public static class ChestInfo implements Serializable {
        /**
         * 宝箱ID
         */
        private String chestId;

        /**
         * 宝箱名称
         */
        private String chestName;

        /**
         * 宝箱图标
         */
        private String chestIcon;

        /**
         * 解锁所需进度
         */
        private Integer unlockProgress;

        /**
         * 宝箱状态
         */
        private String state;

        /**
         * 领取凭证ID
         */
        private String claimId;
    }

    @Data
    @Builder
    public static class ClaimableRewardInfo implements Serializable {
        /**
         * 领取凭证ID
         */
        private String claimId;

        /**
         * 场景编码 (用于前端展示定位)
         */
        private String sceneCode;

        /**
         * 奖励类型
         */
        private String rewardType;

        /**
         * 奖励名称
         */
        private String rewardName;

        /**
         * 奖励图标
         */
        private String rewardIcon;
    }
}
