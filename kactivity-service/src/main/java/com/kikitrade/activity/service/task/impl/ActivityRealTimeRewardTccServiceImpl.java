package com.kikitrade.activity.service.task.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.reward.RewardService;
import com.kikitrade.activity.model.domain.RewardRequest;
import com.kikitrade.activity.service.task.ActivityRealTimeRewardTccService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
@Slf4j
public class ActivityRealTimeRewardTccServiceImpl implements ActivityRealTimeRewardTccService {

    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;
    @Resource
    private RewardService rewardService;

    public void reward(LauncherParameter launcherParameter, TaskConfigDTO taskConfig) throws Exception {
        log.info("[reward] request:{}", JSON.toJSONString(launcherParameter));
        ActivityCustomReward activityCustomReward = launcherParameter.getActivityCustomReward();

        activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.AWARDABLE.name());
        if(taskConfig.getProvideType() == ActivityTaskConstant.ProvideType.auto) {
            activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name());
        }

        if (!insertOrUpdateReward(activityCustomReward)) {
            log.warn("[reward] Failed to insert or update reward: {}", activityCustomReward);
            return;
        }

        if (shouldSendRewardRequest(launcherParameter)) {
            log.info("[reward] Sending reward request for: {}", activityCustomReward);
            sendRewardRequest(activityCustomReward);
        }
    }

    private boolean insertOrUpdateReward(ActivityCustomReward activityCustomReward) {
        log.info("[insertOrUpdateReward] Inserting reward: {}", activityCustomReward);
        boolean success = activityCustomRewardStoreBuilder.insert(activityCustomReward);
        if (!success) {
            log.warn("[insertOrUpdateReward] Insert failed, attempting to update reward status");
            ActivityCustomReward reward = activityCustomRewardStoreBuilder.findByPrimaryId(
                    activityCustomReward.getBatchId(),
                    activityCustomReward.getCustomerId(),
                    activityCustomReward.getSeq()
            );
            if (reward != null && ActivityConstant.RewardStatusEnum.AWARD_FAILED.name().equals(reward.getStatus())) {
                reward.setStatus(ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name());
                success = activityCustomRewardStoreBuilder.updateStatus(reward);
                log.info("[insertOrUpdateReward] Updated reward status to AWARDING: {}", reward);
            }
        }
        return success;
    }

    private boolean shouldSendRewardRequest(LauncherParameter launcherParameter) {
        boolean shouldSend = launcherParameter.getProvideType() == ActivityTaskConstant.ProvideType.auto;
        log.info("[shouldSendRewardRequest] Should send reward request: {}", shouldSend);
        return shouldSend;
    }

    private void sendRewardRequest(ActivityCustomReward activityCustomReward) throws Exception {
        try{
            RewardRequest request = RewardRequest.builder()
                    .customerId(activityCustomReward.getCustomerId())
                    .address(activityCustomReward.getAddress())
                    .rewardId(activityCustomReward.getBusinessId())
                    .type(activityCustomReward.getRewardType())
                    .businessType(activityCustomReward.getBusinessType())
                    .desc(activityCustomReward.getExtendParamMap().getOrDefault("desc", ""))
                    .receiveEndTime(Long.parseLong(activityCustomReward.getExtendParamMap().getOrDefault("receiveEndTime", "0")))
                    .saasId(activityCustomReward.getSaasId())
                    .build();
            log.info("[sendRewardRequest] Sending reward request: {}", request);
            rewardService.reward(request);
        } catch (Exception e) {
            activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.AWARD_FAILED.name());
            activityCustomRewardStoreBuilder.updateStatus(activityCustomReward);
            log.error("[sendRewardRequest] Failed to send reward request: {}", activityCustomReward, e);
        }
    }
}
