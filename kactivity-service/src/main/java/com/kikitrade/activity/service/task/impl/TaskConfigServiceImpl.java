package com.kikitrade.activity.service.task.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.TaskConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.TaskConfig;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.config.TaskCodeConfig;
import com.kikitrade.activity.service.task.TaskConfigService;
import com.kikitrade.activity.service.task.TaskFilter;
import com.kikitrade.kseq.api.SeqClient;
import com.kikitrade.kseq.api.model.SeqRule;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/20 14:39
 */
@Service
@Slf4j
public class TaskConfigServiceImpl implements TaskConfigService {

    @Resource
    private TaskConfigBuilder taskConfigBuilder;
    @Resource
    @Lazy
    private List<TaskFilter> taskFilters;
    @Resource
    private SeqClient seqClient;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private RedisService redisService;

    private SeqRule RULE = new SeqRule("IDX_TASK_CONFIG", 1, "2", null);
    private SeqRule SUB_RULE = new SeqRule("IDX_TASK_CONFIG", 1, "1", null);

    @Override
    public boolean upsert(TaskConfigDTO taskConfigDTO, Boolean isGray) {
       try{
           if(taskConfigDTO.getIsGroup()){
               //创建组任务
               boolean success;
               TaskConfig config = toDO(taskConfigDTO, isGray);
               TaskConfig task = taskConfigBuilder.getTaskById(config.getTaskId());
               log.info("taskConfigService upsert group taskConfig:{}", task);
               if(task == null){
                   success = taskConfigBuilder.insert(config);
               }else{
                   success = taskConfigBuilder.update(config);
               }
               //绑定子任务
               if(success && StringUtils.isNotBlank(taskConfigDTO.getGroupId())){
                   List<TaskConfig> taskConfigs = taskConfigBuilder.getTaskByGroupId(taskConfigDTO.getGroupId());
                   //取消绑定
                   List<String> taskIds = taskConfigs.stream().map(TaskConfig::getTaskId).toList();
                   taskConfigBuilder.delete(taskIds);
               }
               return success;
           }else{
               //创建单任务
               TaskConfig config = toDO(taskConfigDTO, isGray);
               TaskConfig task = taskConfigBuilder.getTaskById(config.getTaskId());
               log.info("taskConfigService upsert taskConfig:{}", task);
               if(task == null){
                   return taskConfigBuilder.insert(config);
               }else{
                   return taskConfigBuilder.update(config);
               }
           }
       }catch (Exception ex){
           log.error("TaskConfigService upsert exception:{}", taskConfigDTO ,ex);
           return false;
       }
    }

    @Override
    public boolean upsert(TaskConfigDTO mainTaskConfigDTO, List<TaskConfigDTO> subTaskConfigDTO) {
        try{
            TaskConfig config = toDO(mainTaskConfigDTO, false);
            List<TaskConfig> configs = subTaskConfigDTO.stream().map(e -> toDO(e,false)).toList();
            //删除原子任务
            List<TaskConfig> taskConfigs = taskConfigBuilder.getTaskByGroupId(mainTaskConfigDTO.getTaskId());
            if(CollectionUtils.isNotEmpty(taskConfigs)){
                taskConfigBuilder.delete(taskConfigs.stream().map(TaskConfig::getTaskId).toList());
            }
            TaskConfig task = taskConfigBuilder.getTaskById(mainTaskConfigDTO.getTaskId());
            taskConfigBuilder.batchInsert(configs);
            if(task == null){
                return taskConfigBuilder.insert(config);
            }else{
                return taskConfigBuilder.update(config);
            }
        }catch (Exception ex){
            return false;
        }
    }

    @Override
    public TaskConfigDTO findByTaskId(String taskId, ActivityTaskConstant.TaskConfigScope... scopes) {
        TaskConfig task = taskConfigBuilder.getTaskById(taskId);
        if(task == null){
            return null;
        }
        if(!task.getStatus().equals("ACTIVE")){
            return null;
        }
        if(task.getStartTime().compareTo(new Date()) > 0 || task.getEndTime().compareTo(new Date()) < 0){
            return null;
        }
        log.info("findByTaskId config:{}", task);
        return toDTO(task, false, scopes);
    }

    @Override
    public TaskConfigDTO findByTaskIdAndWhiteFlag(String taskId, String customerId,
        ActivityTaskConstant.TaskConfigScope... scopes) {
        boolean taskWhiteFlag =  StringUtils.isNotBlank(customerId) && redisService.hGet(RedisKeyConst.TASK_WHITELIST.getMiddleKey(null), customerId) != null;
        log.info("findByTaskIdAndWhiteFlag taskWhiteProperties, customerId:{}, taskWhiteFlag:{}",customerId, taskWhiteFlag);
        TaskConfig task = taskConfigBuilder.getTaskById(taskId);
        if (task == null) {
            return null;
        }
        TaskConfigDTO taskConfigDTO = toDTO(task, taskWhiteFlag, scopes);
        if(taskConfigDTO == null){
            return null;
        }
        if (ActivityConstant.CommonStatus.ACTIVE != taskConfigDTO.getStatus()) {
            return null;
        }
        if (taskConfigDTO.getStartTime().compareTo(new Date().getTime()) > 0 || taskConfigDTO.getEndTime()
                .compareTo(new Date().getTime()) < 0) {
            return null;
        }
        log.info("findByTaskId config:{}", JSON.toJSONString(taskConfigDTO));
        return taskConfigDTO;
    }

    @Override
    public List<TaskConfigDTO> findByGroupTaskId(String groupTaskId, boolean taskWhiteFlag) {
        List<TaskConfig> taskConfigs = taskConfigBuilder.getTaskByGroupId(groupTaskId);
        List<TaskConfigDTO> dtoList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(taskConfigs)){
            for(TaskConfig config : taskConfigs){
                TaskConfigDTO taskConfigDTO = toDTOBase(config, taskWhiteFlag);
                if(taskConfigDTO != null){
                    dtoList.add(taskConfigDTO);
                }
            }
            dtoList.sort(Comparator.comparing(TaskConfigDTO::getOrder));
        }
        return dtoList;
    }

    @Override
    public List<TaskConfigDTO> findByTaskCode(String saasId, String code,
        ActivityTaskConstant.TaskConfigScope... scopes) {
        List<TaskConfig> taskConfigs = taskConfigBuilder.getTaskByCode(saasId, code);
        List<TaskConfigDTO> list = new ArrayList<>();
        for (TaskConfig taskConfig : taskConfigs) {
            if (!taskConfig.getStatus().equals("ACTIVE")) {
                continue;
            }
            if (taskConfig.getStartTime().compareTo(new Date()) > 0 || taskConfig.getEndTime()
                .compareTo(new Date()) < 0) {
                continue;
            }
            TaskConfigDTO taskConfigDTO = toDTO(taskConfig, false, scopes);
            if(taskConfigDTO != null){
                list.add(taskConfigDTO);
            }
        }
        return list;
    }

    @Override
    public List<TaskConfigDTO> findByTaskCodeAndWhiteFlag(String saasId, String code, String customerId,
        ActivityTaskConstant.TaskConfigScope... scopes) {
        boolean taskWhiteFlag =  StringUtils.isNotBlank(customerId) && redisService.hGet(RedisKeyConst.TASK_WHITELIST.getMiddleKey(null), customerId) != null;
        List<TaskConfig> taskConfigs = taskConfigBuilder.getTaskByCode(saasId, code);
        List<TaskConfigDTO> list = new ArrayList<>();
        for (TaskConfig taskConfig : taskConfigs) {
            TaskConfigDTO taskConfigDTO = toDTO(taskConfig, taskWhiteFlag, scopes);
            if(taskConfigDTO == null){
                continue;
            }
            if(taskConfigDTO.getStatus() != ActivityConstant.CommonStatus.ACTIVE){
                continue;
            }
            if (taskConfigDTO.getStartTime().compareTo(new Date().getTime()) > 0 || taskConfigDTO.getEndTime()
                    .compareTo(new Date().getTime()) < 0) {
                continue;
            }
            list.add(taskConfigDTO);
        }
        return list;
    }

    private boolean taskDateLimit(TaskConfig taskConfig, boolean taskWhiteFlag) {
        log.info("taskDateLimit config:{}, taskWhiteFlag:{}", JSON.toJSONString(taskConfig), taskWhiteFlag);
        if (taskWhiteFlag) {
            //配置任务白名单时，提前可查看到任务
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, kactivityProperties.getTaskWhiteDate());
            if (taskConfig.getStartTime().compareTo(calendar.getTime()) > 0 || taskConfig.getEndTime().compareTo(new Date()) < 0) {
                return true;
            }
        } else {
            if (taskConfig.getStartTime().compareTo(new Date()) > 0 || taskConfig.getEndTime().compareTo(new Date()) < 0) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<TaskConfigDTO> findTaskBySaasId(String saasId, String channel, String position, String clientType, boolean taskWhiteFlag) {
        List<TaskConfig> taskConfigs = taskConfigBuilder.findTaskBySaasId(saasId, channel, position);
        List<TaskConfig> taskConfigList = taskConfigs.stream().filter(
            taskConfig -> (StringUtils.isBlank(
                taskConfig.getClientType()) || taskConfig.getClientType().equals(clientType))).toList();
        log.info("[task] findTaskBySaasId request:{}, response:{}", saasId, taskConfigs);
        if (CollectionUtils.isEmpty(taskConfigList)) {
            return null;
        }
        List<TaskConfigDTO> result = new ArrayList<>();
        for(TaskConfig taskConfig : taskConfigList){
            TaskConfigDTO taskConfigDTO = toDTO(taskConfig, taskWhiteFlag);
            if(taskConfigDTO == null){
                continue;
            }
            if(taskConfigDTO.getStatus() != ActivityConstant.CommonStatus.ACTIVE){
                continue;
            }
            if (taskConfigDTO.getStartTime().compareTo(new Date().getTime()) > 0 || taskConfigDTO.getEndTime()
                    .compareTo(new Date().getTime()) < 0) {
                continue;
            }
            result.add(taskConfigDTO);
        }
        return result;
    }

    @Override
    public String nextId(Boolean isSub) {
        if(isSub){
            return seqClient.next(SUB_RULE);
        }
        return seqClient.next(RULE);
    }

    @Override
    public void delete(String id) {
        List<String> ids = new ArrayList<>();
        taskConfigBuilder.delete(ids);
    }

    private TaskConfig toDO(TaskConfigDTO taskConfigDTO, boolean isGray) {
        TaskConfig taskConfig = new TaskConfig();
        taskConfig.setTaskId(taskConfigDTO.getTaskId());
        taskConfig.setGroupId(taskConfigDTO.getGroupId());
        taskConfig.setSaasId(taskConfigDTO.getSaasId());
        taskConfig.setCode(taskConfigDTO.getCode());
        taskConfig.setPosition(taskConfigDTO.getPosition());
        taskConfig.setChannel(taskConfigDTO.getChannel());
        taskConfig.setOnlyVerifySocial(taskConfigDTO.getOnlyVerifySocial());
        taskConfig.setHiddenTaskCompleted(taskConfigDTO.getHiddenTaskCompleted());
        taskConfig.setStatus(ActivityConstant.CommonStatus.ACTIVE.name());
        taskConfig.setTitle(taskConfigDTO.getTitle());
        if (taskConfigDTO.getTitleMap() != null) {
            taskConfig.setTitle(JSON.toJSONString(taskConfigDTO.getTitleMap()));
        }
        taskConfig.setDesc(taskConfigDTO.getDesc());
        if (taskConfigDTO.getDescMap() != null) {
            taskConfig.setDesc(JSON.toJSONString(taskConfigDTO.getDescMap()));
        }
        taskConfig.setLabelName(taskConfigDTO.getLabelName());
        taskConfig.setLabelColor(taskConfigDTO.getLabelColor());
        taskConfig.setImage(JSON.toJSONString(taskConfigDTO.getImage()));
        taskConfig.setStartTime(new Date(taskConfigDTO.getStartTime()));
        taskConfig.setShowList(taskConfigDTO.getShowList());
        taskConfig.setVipLevel(taskConfigDTO.getVipLevel() == null ? "+0" : taskConfigDTO.getVipLevel());
        taskConfig.setLedgerTitle(taskConfigDTO.getLedgerTitle());
        if(taskConfigDTO.getLink() != null){
            taskConfig.setLink(JSON.toJSONString(taskConfigDTO.getLink()));
        }
        if (taskConfigDTO.getEndTime() != null) {
            taskConfig.setEndTime(new Date(taskConfigDTO.getEndTime()));
        }
        taskConfig.setCode(taskConfigDTO.getCode());
        taskConfig.setShowCode(taskConfigDTO.getShowCode());
        taskConfig.setLimit(taskConfigDTO.getLimit());
        taskConfig.setLimitMap(JSON.toJSONString(taskConfigDTO.getLimitMap()));
        taskConfig.setRewardFrequency(taskConfigDTO.getRewardFrequency());
        taskConfig.setBtn(taskConfigDTO.getBtn());
        if (taskConfigDTO.getCycle() != null) {
            taskConfig.setCycle(taskConfigDTO.getCycle().name());
        }
        if (taskConfigDTO.getProgressType() != null) {
            taskConfig.setProgressType(taskConfigDTO.getProgressType().name());
        }
        if (taskConfigDTO.getRewardForm() != null) {
            taskConfig.setRewardForm(taskConfigDTO.getRewardForm().name());
        }
        if (taskConfigDTO.getProvideType() != null) {
            taskConfig.setProvideType(taskConfigDTO.getProvideType().name());
        }

        taskConfig.setUrl(taskConfigDTO.getUrl());
        taskConfig.setConnectUrl(taskConfigDTO.getConnectUrl());
        taskConfig.setConnectUrlPc(taskConfigDTO.getConnectUrlPc());
        if (taskConfigDTO.getReward() != null) {
            taskConfig.setReward(JSON.toJSONString(taskConfigDTO.getReward()));
        }
        taskConfig.setOrder(taskConfigDTO.getOrder());
        taskConfig.setDomain(taskConfigDTO.getDomain());

        if (taskConfigDTO.getAttr() != null) {
            taskConfig.setAttr(JSON.toJSONString(taskConfigDTO.getAttr()));
        }
        taskConfig.setShowProgress(taskConfigDTO.getShowProgress() == null || taskConfigDTO.getShowProgress());
        taskConfig.setLastPost(taskConfigDTO.getLastPost());
        taskConfig.setSkipVerification(taskConfigDTO.getSkipVerification());
        taskConfig.setCallRegister(taskConfigDTO.getCallRegister());
        taskConfig.setClientType(taskConfigDTO.getClientType());
        taskConfig.setType(taskConfigDTO.getType());
        if (!Objects.isNull(taskConfigDTO.getCheckReward())) {
            taskConfig.setCheckReward(taskConfigDTO.getCheckReward());
        }
        taskConfig.setPostTaskId(taskConfigDTO.getPostTaskId());
        taskConfig.setPostTaskReward(taskConfigDTO.getPostTaskReward());
        taskConfig.setPostTaskDesc(taskConfigDTO.getPostTaskDesc());
        taskConfig.setPostTaskCode(taskConfigDTO.getPostTaskCode());
        return taskConfig;
    }

    private TaskConfigDTO toDTO(TaskConfig taskConfig, Boolean taskWhiteFlag ,ActivityTaskConstant.TaskConfigScope... scope) {

        TaskConfigDTO taskConfigDTO = toDTOBase(taskConfig, taskWhiteFlag);
        if(taskConfigDTO == null){
            return null;
        }
        //构建子任务
        if(scope != null && Arrays.stream(scope).anyMatch(s -> s == ActivityTaskConstant.TaskConfigScope.SUB_TASK)){
            List<TaskConfig> taskConfigs = taskConfigBuilder.getTaskByGroupId(taskConfig.getTaskId());
            List<TaskConfigDTO> dtoList = new ArrayList<>();
            for(TaskConfig config : taskConfigs){
                TaskConfigDTO taskConfigDTO1 = toDTOBase(config, taskWhiteFlag);
                if(taskConfigDTO1 != null){
                    dtoList.add(taskConfigDTO1);
                }
            }
            dtoList.sort(Comparator.comparing(TaskConfigDTO::getOrder));
            taskConfigDTO.setSubTask(dtoList);
        }

        //构建 filter
        if(scope != null && Arrays.stream(scope).anyMatch(s -> s == ActivityTaskConstant.TaskConfigScope.FILTER)){
            List<TaskFilter> filters = new ArrayList<>();
            //取 common filter
            List<TaskFilter> commonFilters = taskFilters.stream().filter(t -> TaskFilter.FilterScope.COMMON == t.getScope()).toList();
            if(CollectionUtils.isNotEmpty(commonFilters)){
                filters.addAll(commonFilters);
            }
            //取 code filter
            List<TaskFilter> codeFilters = TaskCodeConfig.getValue(taskConfig.getCode()).getAction(taskFilters, "kactivity");
            if(CollectionUtils.isNotEmpty(codeFilters)){
                filters.addAll(codeFilters);
            }
            //取 task 私有 filter
            List<String> privateFilter = JSON.parseArray(taskConfig.getFilter(), String.class);
            if(CollectionUtils.isNotEmpty(taskFilters) && CollectionUtils.isNotEmpty(privateFilter)){
                List<TaskFilter> filters1 = taskFilters.stream().filter(t -> privateFilter.contains(SpringUtil.getBeanNamesForType(t.getClass())[0])).toList();
                if(CollectionUtils.isNotEmpty(filters1)){
                    filters.addAll(filters1);
                }
            }
            taskConfigDTO.setFilters(filters);
        }
        return taskConfigDTO;
    }

    private TaskConfigDTO toDTOBase(TaskConfig taskConfig, Boolean taskWhiteFlag) {
        TaskConfigDTO taskConfigDTO = new TaskConfigDTO();
        taskConfigDTO.setTaskId(taskConfig.getTaskId());
        taskConfigDTO.setGroupId(taskConfig.getGroupId());
        taskConfigDTO.setSaasId(taskConfig.getSaasId());
        taskConfigDTO.setCode(taskConfig.getCode());
        taskConfigDTO.setChannel(taskConfig.getChannel());
        taskConfigDTO.setPosition(taskConfig.getPosition());
        taskConfigDTO.setClientType(taskConfig.getClientType());
        taskConfigDTO.setOnlyVerifySocial(taskConfig.getOnlyVerifySocial());
        taskConfigDTO.setHiddenTaskCompleted(taskConfig.getHiddenTaskCompleted());

        if(ActivityConstant.CommonStatus.ACTIVE.name().equals(taskConfig.getStatus())){
            taskConfigDTO.setStatus(ActivityConstant.CommonStatus.valueOf(taskConfig.getStatus()));
            taskConfigDTO.setTitle(taskConfig.getTitle());
            taskConfigDTO.setDesc(taskConfig.getDesc());
            taskConfigDTO.setLabelName(taskConfig.getLabelName());
            taskConfigDTO.setLabelColor(taskConfig.getLabelColor());
            taskConfigDTO.setImage(taskConfig.getImageMap());
            taskConfigDTO.setStartTime(taskConfig.getStartTime().getTime());
            taskConfigDTO.setVipLevel(taskConfig.getVipLevel());
            if(taskConfig.getEndTime() != null){
                taskConfigDTO.setEndTime(taskConfig.getEndTime().getTime());
            }
            taskConfigDTO.setShowCode(taskConfig.getShowCode());
            taskConfigDTO.setLimit(taskConfig.getLimit());
            if(taskConfig.getLimitMap() != null){
                taskConfigDTO.setLimitMap(JSON.parseObject(taskConfig.getLimitMap(), new HashMap<String, Integer>().getClass()));
            }
            taskConfigDTO.setRewardFrequency(taskConfig.getRewardFrequency());
            if(taskConfig.getCycle() != null){
                taskConfigDTO.setCycle(ActivityTaskConstant.TaskCycleEnum.valueOf(taskConfig.getCycle()));
            }
            if(taskConfig.getProgressType() != null){
                taskConfigDTO.setProgressType(ActivityTaskConstant.ProgressTypeEnum.valueOf(taskConfig.getProgressType()));
            }
            if(taskConfig.getRewardForm() != null){
                taskConfigDTO.setRewardForm(ActivityTaskConstant.RewardForm.valueOf(taskConfig.getRewardForm()));
            }
            if(taskConfig.getProvideType() != null){
                taskConfigDTO.setProvideType(ActivityTaskConstant.ProvideType.valueOf(taskConfig.getProvideType()));
            }
            taskConfigDTO.setUrl(taskConfig.getUrl());
            taskConfigDTO.setConnectUrl(taskConfig.getConnectUrl());
            taskConfigDTO.setConnectUrlPc(taskConfig.getConnectUrlPc());
            if(taskConfig.getReward() != null){
                taskConfigDTO.setReward(taskConfig.getRewardGroup());
            }
            taskConfigDTO.setOrder(taskConfig.getOrder());
            taskConfigDTO.setAttr(taskConfig.getAttrMap());
            taskConfigDTO.setBtn(taskConfig.getBtn());
            taskConfigDTO.setDomain(taskConfig.getDomain());
            if(MapUtils.isNotEmpty(taskConfig.getLinkMap())){
                taskConfigDTO.setLink(taskConfig.getLinkMap());
            }
            taskConfigDTO.setShowProgress(taskConfig.getShowProgress());
            taskConfigDTO.setLedgerTitle(taskConfig.getLedgerTitle());
            taskConfigDTO.setLastPost(BooleanUtils.isTrue(taskConfig.getLastPost()));
            taskConfigDTO.setCallRegister(BooleanUtils.isNotFalse(taskConfig.getCallRegister()));
            taskConfigDTO.setPosition(taskConfig.getPosition());
            taskConfigDTO.setSkipVerification(BooleanUtils.isTrue(taskConfig.getSkipVerification()));
            taskConfigDTO.setClientType(taskConfig.getClientType());
            taskConfigDTO.setType(taskConfig.getType());
            taskConfigDTO.setCheckReward(taskConfig.getCheckReward());
            taskConfigDTO.setPostTaskId(taskConfig.getPostTaskId());
            taskConfigDTO.setPostTaskDesc(taskConfig.getPostTaskDesc());
            taskConfigDTO.setPostTaskReward(taskConfig.getPostTaskReward());
            taskConfigDTO.setPostTaskCode(taskConfig.getPostTaskCode());
            return taskConfigDTO;
        }else{
            return null;
        }
    }

}
