package com.kikitrade.activity.service.draw;

import com.kikitrade.activity.dal.tablestore.model.GiftPackConfig;
import com.kikitrade.activity.dal.tablestore.model.RandomRewardPool;

import java.util.List;

/**
 * 奖励发放服务接口
 * 按照技术规格书要求实现动态奖励组合逻辑
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface RewardIssueService {
    
    /**
     * 根据礼包ID发放奖励
     * 严格按照技术规格书中定义的流程：
     * 1. 查询 gift_pack_config 表获取礼包配置
     * 2. 根据规则类型处理固定物品或随机池抽取
     * 3. 对于随机池，查询 random_reward_pool 表并按权重随机抽取
     * 
     * @param packId 礼包ID
     * @param saasId SaaS ID
     * @return 发放的奖励列表
     */
    List<IssuedReward> issueRewardsByPackId(String packId, String saasId);

    /**
     * 根据宝箱ID发放奖励
     * 严格按照技术规格书中定义的流程：
     * 1. 查询 chest_config 表获取宝箱配置
     * 2. 根据规则类型处理固定物品或随机池抽取
     * 3. 对于随机池，查询 random_reward_pool 表并按权重随机抽取
     *
     * @param chestId 宝箱ID
     * @param saasId SaaS ID
     * @return 发放的奖励列表
     */
    List<IssuedReward> issueRewardsByChestId(String chestId, String saasId);

    /**
     * 根据宝箱ID和用户偏好发放奖励（支持新的偏好映射逻辑）
     *
     * @param chestId 宝箱ID
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param userPreferences 用户偏好映射
     * @return 发放的奖励列表
     */
    List<IssuedReward> issueRewardsByChestIdWithPreferences(String chestId, String userId, String saasId, java.util.Map<String, String> userPreferences);

    /**
     * 直接发放指定道具
     *
     * @param itemId 道具ID
     * @param quantity 数量
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @return 发放的奖励
     */
    IssuedReward issueDirectItem(String itemId, Integer quantity, String userId, String saasId);

    /**
     * 从随机奖励池中按权重随机抽取奖励
     * 
     * @param poolId 随机池ID
     * @param pickCount 抽取数量
     * @param saasId SaaS ID
     * @return 抽取的奖励列表
     */
    List<IssuedReward> pickFromRandomPool(String poolId, Integer pickCount, String saasId);
    
    /**
     * 处理固定物品奖励
     * 
     * @param giftPackConfig 礼包配置
     * @return 发放的奖励
     */
    IssuedReward processFixedItem(GiftPackConfig giftPackConfig);
    
    /**
     * 按权重随机选择奖励池中的物品
     * 
     * @param rewardPools 奖励池列表
     * @param count 选择数量
     * @return 选中的奖励池配置
     */
    List<RandomRewardPool> selectByWeight(List<RandomRewardPool> rewardPools, int count);
    
    /**
     * 发放的奖励信息
     */
    class IssuedReward {
        private String itemId;
        private String itemType;
        private String itemName;
        private Integer quantity;
        private String description;
        private String iconUrl;
        
        public IssuedReward() {}
        
        public IssuedReward(String itemId, String itemType, String itemName, Integer quantity) {
            this.itemId = itemId;
            this.itemType = itemType;
            this.itemName = itemName;
            this.quantity = quantity;
        }
        
        public IssuedReward(String itemId, String itemType, String itemName, Integer quantity, 
                           String description, String iconUrl) {
            this.itemId = itemId;
            this.itemType = itemType;
            this.itemName = itemName;
            this.quantity = quantity;
            this.description = description;
            this.iconUrl = iconUrl;
        }
        
        // Getters and Setters
        public String getItemId() { return itemId; }
        public void setItemId(String itemId) { this.itemId = itemId; }
        
        public String getItemType() { return itemType; }
        public void setItemType(String itemType) { this.itemType = itemType; }
        
        public String getItemName() { return itemName; }
        public void setItemName(String itemName) { this.itemName = itemName; }
        
        public Integer getQuantity() { return quantity; }
        public void setQuantity(Integer quantity) { this.quantity = quantity; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public String getIconUrl() { return iconUrl; }
        public void setIconUrl(String iconUrl) { this.iconUrl = iconUrl; }
        
        @Override
        public String toString() {
            return itemName + " x" + quantity;
        }
    }
}
