package com.kikitrade.activity.service.remote.impl;

import cn.hutool.core.lang.TypeReference;
import com.alibaba.fastjson2.JSON;
import com.kikitrade.activity.api.RemoteLotteryService;
import com.kikitrade.activity.api.exception.ActivityException;
import com.kikitrade.activity.api.model.draw.ExchangeTicketsDTO;
import com.kikitrade.activity.api.model.request.reward.*;
import com.kikitrade.activity.api.model.response.reward.*;
import com.kikitrade.activity.dal.tablestore.builder.*;
import com.kikitrade.activity.dal.tablestore.model.*;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.draw.*;
import com.kikitrade.activity.service.draw.preference.UserPreferenceService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.StringUtils;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 奖励中台服务实现
 * 提供新的奖励中台功能，与现有RemoteLotteryService并行运行
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@DubboService
@Slf4j
public class RemoteLotteryServiceImpl implements RemoteLotteryService {

    @Resource
    private PrizePoolBuilder prizePoolBuilder;

    @Resource
    private PrizeConfigBuilder prizeConfigBuilder;

    @Resource
    private ProbabilityAlgorithmService probabilityAlgorithmService;

    @Resource
    private DynamicPrizePoolBuilder dynamicPrizePoolBuilder;

    @Resource
    private UserPreferenceService userPreferenceService;

    @Resource
    private LotteryTicketService lotteryTicketService;

    @Resource
    private UserProgressTrackerBuilder userProgressTrackerBuilder;

    @Resource
    private UnifiedClaimService unifiedClaimService;

    @Resource
    private ProgressChestConfigBuilder progressChestConfigBuilder;

    @Resource
    private UserClaimEntitlementBuilder userClaimEntitlementBuilder;

    @Resource
    private RewardIssueService rewardIssueService;

    @Resource
    private DirectRewardIssuanceLogBuilder directRewardIssuanceLogBuilder;

    @Resource
    private DrawHistoryBuilder drawHistoryBuilder;

    @Override
    public ExchangeTicketsResponse exchangeTickets(ExchangeTicketsRequest request) {
        log.info("用户兑换抽奖券（两阶段抽奖系统）: userId={}, prizePoolCode={}, exchangeType={}, transferOutAssetType={}, optionId={}",
                request.getCustomerId(), request.getPrizePoolCode(), request.getExchangeType(), request.getAssetType(), request.getOptionId());

        // 委托给专门的抽奖券服务处理
        return lotteryTicketService.exchangeTickets(request);
    }

    @Override
    public DrawBatchResponse drawBatch(DrawBatchRequest request) {
        log.info("用户批量抽奖: userId={}, prizePoolCode={}, drawCount={}",
                request.getUserId(), request.getPrizePoolCode(), request.getDrawCount());

        // 第1步：准备阶段 - 生成批量交易ID
        String batchTransactionId = "draw-batch-" + UUID.randomUUID().toString();
        log.info("生成批量交易ID: batchTransactionId={}", batchTransactionId);

        // 初始化内存列表，用于存放抽奖历史记录
        List<DrawHistory> historyRecordsToSave = new ArrayList<>();


            //这里先冻结资产，等抽奖结束后解冻消耗资产，如果抽奖失败只需要解冻
            ExchangeTicketsDTO exchangeTicketsDTO =
                ExchangeTicketsDTO.builder()
                    .userId(request.getUserId())
                    .saasId(request.getSaasId())
                    .transferOutAssetType("TICKET_" + request.getPrizePoolCode())
                    .transferOutAmount(String.valueOf(request.getDrawCount()))
                    .businessId(OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ActivityConstant.AssetOperateType.FREEZE.getCode() + request.getUserId())
                    .businessType("SPIN_A_WHEEL")
                    .desc("freeze tickets")
                    .build();
        try {
            lotteryTicketService.freezeAsset(exchangeTicketsDTO);
            // 1. 验证奖池配置
            PrizePool prizePool = prizePoolBuilder.findByCodeAndSaasId(request.getPrizePoolCode(), request.getSaasId());
            if (prizePool == null) {
                return DrawBatchResponse.builder().success(false).errorCode("POOL_NOT_FOUND").message("奖池不存在")
                    .build();
            }

            // 2. 构建动态奖池（使用新的动态奖池构建器）
            List<PrizeConfig> dynamicPool =
                dynamicPrizePoolBuilder.buildDynamicPool(request.getUserId(), request.getPrizePoolCode(),
                    request.getSaasId());

            if (dynamicPool.isEmpty()) {
                return DrawBatchResponse.builder().success(false).errorCode("NO_PRIZES_AVAILABLE")
                    .message("暂无可用奖品").build();
            }

            // 3. 获取兜底奖品（SINGLE策略需要）
            PrizeConfig fallbackPrize = null;
            if (ActivityConstant.LotteryStrategyEnum.SINGLE.name().equals(prizePool.getProbabilityStrategy()) && prizePool.getFallbackPrizeConfigId() != null) {
                fallbackPrize = prizeConfigBuilder.findById(request.getSaasId(), request.getPrizePoolCode(), prizePool.getFallbackPrizeConfigId());
            }

            // 第2步：真正的批量抽奖（一次调用完成所有抽奖）
            log.info("开始批量抽奖: drawCount={}", request.getDrawCount());
            List<PrizeConfig> batchResults = probabilityAlgorithmService.batchDraw(
                dynamicPool, prizePool.getProbabilityStrategy(), fallbackPrize, request.getDrawCount());

            if (batchResults.isEmpty()) {
                log.error("批量抽奖失败: 无可用奖品");
                throw new ActivityException("DRAW_FAILED");
            }

            log.info("批量抽奖完成: userId={}, 获得奖品数量={}", request.getUserId(), batchResults.size());

            // 第3步：批量生成历史记录
            historyRecordsToSave = generateBatchHistoryRecords(batchResults, request, batchTransactionId, exchangeTicketsDTO);

            log.info("批量生成历史记录完成: userId={}, 生成历史记录数量={}", request.getUserId(), historyRecordsToSave.size());

            // 第4步：数据库落库 - 批量插入历史记录
            log.info("开始批量落库抽奖历史记录: 记录数量={}", historyRecordsToSave.size());
            boolean historyInsertResult = drawHistoryBuilder.batchInsert(historyRecordsToSave);

            if (!historyInsertResult) {
                log.error("批量插入抽奖历史记录失败: userId={}, batchTransactionId={}",
                         request.getUserId(), batchTransactionId);
                throw new ActivityException("HISTORY_INSERT_FAILED");
            }

            log.info("批量插入抽奖历史记录成功: userId={}, batchTransactionId={}, 记录数量={}",
                    request.getUserId(), batchTransactionId, historyRecordsToSave.size());

            // 第5步：直接发放奖励

            // 第6步：汇总结果
            List<DrawBatchResponse.DrawResult> aggregatedResults = aggregateDrawResults(batchResults);

            log.info("批量抽奖结果汇总: userId={}, 汇总后奖品种类={}", request.getUserId(), aggregatedResults.size());

            // 更新用户进度
            updateUserProgress(request.getSaasId(), request.getUserId(), request.getPrizePoolCode(),
                request.getDrawCount(), prizePool);

            //解冻资产并扣减
            lotteryTicketService.unfreezeSubtractAsset(exchangeTicketsDTO);
            // 构建完整的响应对象
            DrawBatchResponse.DrawBatchResponseBuilder responseBuilder =
                DrawBatchResponse.builder().success(true).message("抽奖成功").drawResults(aggregatedResults);
            return responseBuilder.build();
        } catch (ActivityException e) {
            log.error("用户批量抽奖失败", e);
            return DrawBatchResponse.builder()
                    .success(false)
                    .errorCode(e.getCode())
                    .message(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("用户批量抽奖失败", e);
            lotteryTicketService.unfreezeAsset(exchangeTicketsDTO);
            return DrawBatchResponse.builder()
                    .success(false)
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }

    /**
     * 更新用户进度并检查宝箱解锁
     */
    private void updateUserProgress(String saasId, String userId, String prizePoolCode, int drawCount, PrizePool prizePool) {
        try {
            // 1. 获取当前用户进度
            UserProgressTracker userProgressTracker = userProgressTrackerBuilder.findByUserIdAndPrizePoolCode(userId, prizePoolCode);
            long now = System.currentTimeMillis();
            long oldProgress = userProgressTracker != null ? userProgressTracker.getCurrentProgress() : 0;

            if (userProgressTracker == null) {
                // 2.1 新用户,创建进度记录
                userProgressTracker = new UserProgressTracker();
                userProgressTracker.setUserId(userId);
                userProgressTracker.setPrizePoolCode(prizePoolCode);
                userProgressTracker.setCurrentProgress(drawCount);
                userProgressTracker.setCycleStartTime(OffsetDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.DAYS).toInstant().toEpochMilli());
                userProgressTrackerBuilder.insert(userProgressTracker);
            } else {
                // 2.2 检查周期是否过期
                long cycleDuration = TimeUnit.DAYS.toMillis(prizePool.getChestCycleDays());
                boolean cycleExpired = now > (userProgressTracker.getCycleStartTime() + cycleDuration);

                if (cycleExpired) {
                    // 周期已过期,重置进度
                    userProgressTracker.setCurrentProgress(drawCount);
                    userProgressTracker.setCycleStartTime(OffsetDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.DAYS).toInstant().toEpochMilli());
                    userProgressTrackerBuilder.insert(userProgressTracker);
                } else {
                    // 周期未过期,累加进度
                    userProgressTracker.setCurrentProgress(userProgressTracker.getCurrentProgress() + drawCount);
                    userProgressTracker.setUpdateTime(now);
                    // 3. 保存更新后的进度
                    userProgressTrackerBuilder.update(userProgressTracker);
                }
            }

            // 4. 检查是否达到宝箱解锁条件
            checkAndGrantChestEntitlements(saasId, userId, prizePoolCode, oldProgress, userProgressTracker.getCurrentProgress());

        } catch (Exception e) {
            log.error("更新用户进度异常: userId={}, prizePoolCode={}", userId, prizePoolCode, e);
            // 不抛出异常,避免影响主流程
        }
    }

    /**
     * 检查并授予宝箱解锁资格
     */
    private void checkAndGrantChestEntitlements(String saasId, String userId, String prizePoolCode, long oldProgress, long newProgress) {
        try {
            // 1. 获取该奖池关联的所有宝箱配置
            List<ProgressChestConfig> chestConfigs = progressChestConfigBuilder.findBySaasIdAndPrizePoolCode(saasId, prizePoolCode);

            // 2. 获取当前用户的进度记录
            UserProgressTracker progressTracker = userProgressTrackerBuilder.findByUserIdAndPrizePoolCode(userId, prizePoolCode);
            if (progressTracker == null) {
                log.error("用户进度记录不存在: userId={}, prizePoolCode={}", userId, prizePoolCode);
                return;
            }

            // 3. 遍历所有宝箱配置
            for (ProgressChestConfig chest : chestConfigs) {
                try {
                    // 3.1 检查是否是新解锁的宝箱（跨越解锁阈值）
                    if (newProgress >= chest.getUnlockProgress() && oldProgress < chest.getUnlockProgress()) {
                        log.info("检测到宝箱新解锁: userId={}, chestId={}, oldProgress={}, newProgress={}, unlockProgress={}",
                            userId, chest.getChestId(), oldProgress, newProgress, chest.getUnlockProgress());

                        // 3.2 查询本周期内是否已经生成过凭证
                        UserClaimEntitlement existingEntitlements = userClaimEntitlementBuilder.findUnClaimedChest(
                            userId,
                            chest.getChestId(),
                            progressTracker.getCycleStartTime()
                        );

                        if (existingEntitlements != null) {
                            log.info("本周期内已存在宝箱凭证: userId={}, chestId={}, cycleStartTime={}",
                                userId, chest.getChestId(), progressTracker.getCycleStartTime());
                            continue;
                        }

                        // 3.3 计算凭证过期时间（与进度周期对齐）
                        long cycleDuration = TimeUnit.DAYS.toMillis(chest.getResetCycleDays());
                        long expireTime = progressTracker.getCycleStartTime() + cycleDuration;

                        // 3.4 生成新的领取凭证
                        UnifiedClaimService.CreateEntitlementRequest request = UnifiedClaimService.CreateEntitlementRequest.builder()
                            .userId(userId)
                            .saasId(saasId)
                            .sceneCode(chest.getSceneCode())
                            .rewardType(ActivityConstant.RewardTypeEnum.PROGRESS_CHEST.name())
                            .rewardSourceId(chest.getChestId())
                            .rewardName(chest.getChestName())
                            .rewardIcon(chest.getIconUrl())
                            .rewardDescription(chest.getDescription())
                            .sourceChannel("PROGRESS_TRACKING")
                            .sourceTransactionId("PROGRESS_TRACKING_" + chest.getChestId() + "_" + userId + "_" + progressTracker.getCycleStartTime())
                            .expireTime(expireTime)
                            .build();
                        unifiedClaimService.createEntitlement(request);
                        log.info("成功生成宝箱凭证: userId={}, chestId={}, expireTime={}, cycleStartTime={}",
                            userId, chest.getChestId(), expireTime, progressTracker.getCycleStartTime());
                    }
                } catch (Exception e) {
                    log.error("处理单个宝箱解锁异常: userId={}, chestId={}", userId, chest.getChestId(), e);
                    // 继续处理下一个宝箱
                }
            }
        } catch (Exception e) {
            log.error("检查宝箱解锁异常: userId={}, prizePoolCode={}", userId, prizePoolCode, e);
        }
    }

    // ==================== 用户偏好管理API ====================

    @Override
    public SetUserPreferenceResponse setUserPreference(SetUserPreferenceRequest request) {
        log.info("设置用户偏好: userId={}, prizePoolCode={}, type={}, value={}",
                request.getUserId(), request.getPrizePoolCode(), request.getPreferenceType(), request.getPreferenceValue());

        try {
            // 设置新的偏好值
            boolean success = userPreferenceService.setUserPreference(
                    request.getUserId(), request.getPrizePoolCode(),
                    request.getPreferenceType(), request.getPreferenceValue());

            return SetUserPreferenceResponse.builder()
                    .success(success)
                    .message(success ? "偏好设置成功" : "偏好设置失败")
                    .build();

        } catch (Exception e) {
            log.error("设置用户偏好失败", e);
            return SetUserPreferenceResponse.builder()
                    .success(false)
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }

    @Override
    public GetUserPreferenceResponse getUserPreference(GetUserPreferenceRequest request) {
        log.info("获取用户偏好: userId={}, saasId={}, type={}",
                request.getUserId(), request.getSaasId(), request.getPreferenceType());

        try {
            String preferenceValue = userPreferenceService.getUserPreference(
                    request.getUserId(), request.getSaasId(), request.getPreferenceType());

            return GetUserPreferenceResponse.builder()
                    .success(true)
                    .preferenceType(request.getPreferenceType())
                    .preferenceValue(preferenceValue)
                    .message("查询成功")
                    .build();

        } catch (Exception e) {
            log.error("获取用户偏好失败", e);
            return GetUserPreferenceResponse.builder()
                    .success(false)
                    .preferenceType(request.getPreferenceType())
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }

    // ==================== 抽奖状态查询API ====================

    @Override
    public LotteryStateResponse getLotteryState(LotteryStateRequest request) {
        log.info("获取用户抽奖状态: userId={}, prizePoolCode={}",
                request.getUserId(), request.getPrizePoolCode());

        try {
            // 1. 获取用户偏好信息及进度信息
            LotteryStateResponse.UserProfileInfo userInfo = buildUserProfileInfo(request.getSaasId(), request.getUserId(), request.getPrizePoolCode());

            // 2. 获取奖池信息
            LotteryStateResponse.PrizePoolInfo prizePoolInfo = buildPrizePoolInfo(request.getPrizePoolCode(), request.getSaasId());

            // 3. 获取进度宝箱信息
            List<LotteryStateResponse.ChestInfo> chestInfo = buildChestInfo(request.getSaasId(), request.getUserId(), request.getPrizePoolCode());

            // 4. 获取可领取奖励信息
            List<LotteryStateResponse.ClaimableRewardInfo> claimableRewards = new ArrayList<>();
            claimableRewards = buildClaimableRewardInfo(request.getUserId(), request.getSaasId());

            log.info("用户抽奖状态查询成功: userInfo={}, prizePoolInfo={}, chestInfo={}, claimableRewards={}",
                    JSON.toJSONString(userInfo), JSON.toJSONString(prizePoolInfo), JSON.toJSONString(chestInfo), JSON.toJSONString(claimableRewards));

            LotteryStateResponse.LotteryStateData data = LotteryStateResponse.LotteryStateData.builder()
                    .prizePool(prizePoolInfo)
                    .userProfile(userInfo)
                    .chests(chestInfo)
                    .claimableRewards(claimableRewards)
                    .build();

            return LotteryStateResponse.builder()
                    .success(true)
                    .data(data)
                    .message("查询成功")
                    .build();

        } catch (Exception e) {
            log.error("获取用户抽奖状态异常", e);
            return LotteryStateResponse.builder()
                    .success(false)
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }
    @Override
    public GrantPackResponse grantPackEntitlement(GrantPackRequest request) {
        log.info("授予礼包权益: userId={}, rewardType={}, rewardSourceId={}, sourceChannel={}, transactionId={}",
                request.getUserId(), request.getRewardType(), request.getRewardSourceId(),
                request.getSourceChannel(), request.getSourceTransactionId());

        try {
            // 1. 参数验证
            if (!validateGrantPackRequest(request)) {
                return GrantPackResponse.builder()
                        .success(false)
                        .errorCode("INVALID_PARAMS")
                        .message("请求参数不完整")
                        .build();
            }

            // 2. 幂等性检查 - 根据sourceTransactionId查询是否已存在凭证
            UserClaimEntitlement existingEntitlement = userClaimEntitlementBuilder
                    .findBySourceTransactionId(request.getSourceTransactionId(), request.getSaasId());

            if (existingEntitlement != null) {
                log.info("检测到重复请求，返回已存在的凭证: claimId={}, transactionId={}",
                        existingEntitlement.getClaimId(), request.getSourceTransactionId());

                return GrantPackResponse.builder()
                        .success(true)
                        .claimId(existingEntitlement.getClaimId())
                        .message("凭证已存在")
                        .createTime(existingEntitlement.getCreateTime())
                        .expireTime(request.getExpireTime())
                        .isDuplicate(true)
                        .build();
            }

            // 3. 创建领奖凭证
            UnifiedClaimService.CreateEntitlementRequest entitlementRequest =
                    UnifiedClaimService.CreateEntitlementRequest.builder()
                            .userId(request.getUserId())
                            .saasId(request.getSaasId())
                            .sceneCode(request.getSceneCode())
                            .rewardType(request.getRewardType().name())
                            .rewardSourceId(request.getRewardSourceId())
                            .rewardName(request.getRewardName())
                            .rewardIcon(request.getRewardIcon())
                            .rewardDescription(request.getRewardDescription())
                            .sourceChannel(request.getSourceChannel())
                            .sourceTransactionId(request.getSourceTransactionId())
                            .expireTime(request.getExpireTime())
                            .build();

            UnifiedClaimService.EntitlementCreateResult entitlement =
                unifiedClaimService.createEntitlement(entitlementRequest);

            if (entitlement != null && entitlement.isSuccess()) {
                String claimId = entitlement.getClaimId();
                log.info("成功创建领奖凭证: userId={}, claimId={}, transactionId={}",
                        request.getUserId(), claimId, request.getSourceTransactionId());

                return GrantPackResponse.builder()
                        .success(true)
                        .claimId(claimId)
                        .message("凭证创建成功")
                        .createTime(System.currentTimeMillis())
                        .expireTime(request.getExpireTime())
                        .isDuplicate(false)
                        .build();
            } else {
                log.error("创建领奖凭证失败: userId={}, transactionId={}",
                        request.getUserId(), request.getSourceTransactionId());

                return GrantPackResponse.builder()
                        .success(false)
                        .errorCode("CREATE_ENTITLEMENT_FAILED")
                        .message("凭证创建失败")
                        .build();
            }

        } catch (Exception e) {
            log.error("授予礼包权益异常: userId={}, transactionId={}",
                    request.getUserId(), request.getSourceTransactionId(), e);

            return GrantPackResponse.builder()
                    .success(false)
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }

    @Override
    public IssuePackResponse issuePack(IssuePackRequest request) {
        log.info("发放礼包: userId={}, packId={}, channel={}, transactionId={}",
                request.getUserId(), request.getPackId(), request.getChannel(), request.getTransactionId());

        try {
            // 1. 参数验证
            if (!validateIssuePackRequest(request)) {
                return IssuePackResponse.builder()
                        .success(false)
                        .errorCode("INVALID_PARAMS")
                        .message("请求参数不完整")
                        .build();
            }

            // 2. 幂等性检查 - 根据transactionId查询是否已发放
            DirectRewardIssuanceLog existingLog = directRewardIssuanceLogBuilder
                    .findByTransactionId(request.getTransactionId());

            if (existingLog != null) {
                log.info("检测到重复请求，返回已发放的结果: transactionId={}, status={}",
                        request.getTransactionId(), existingLog.getIssueStatus());

                if ("SUCCESS".equals(existingLog.getIssueStatus())) {
                    // 解析已发放的奖励
                    List<IssuePackResponse.IssuedReward> issuedRewards = parseIssuedRewardsFromLog(existingLog.getRewardsContent());

                    return IssuePackResponse.builder()
                            .success(true)
                            .packId(request.getPackId())
                            .issuedRewards(issuedRewards)
                            .message("礼包已发放")
                            .issueTime(existingLog.getCreateTime())
                            .transactionId(request.getTransactionId())
                            .isDuplicate(true)
                            .build();
                } else {
                    return IssuePackResponse.builder()
                            .success(false)
                            .packId(request.getPackId())
                            .errorCode("PREVIOUS_FAILED")
                            .message("之前的发放请求失败: " + existingLog.getFailureReason())
                            .transactionId(request.getTransactionId())
                            .isDuplicate(true)
                            .build();
                }
            }

            // 3. 记录发放流水（处理中状态）
            DirectRewardIssuanceLog issuanceLog = createIssuanceLog(request, "PROCESSING", null, null);
            directRewardIssuanceLogBuilder.insert(issuanceLog);

            // 4. 使用RewardIssueService发放礼包奖励
            List<RewardIssueService.IssuedReward> serviceRewards = rewardIssueService
                    .issueRewardsByPackId(request.getPackId(), request.getSaasId());

            if (serviceRewards.isEmpty()) {
                // 更新流水状态为失败
                issuanceLog.setIssueStatus("FAILED");
                issuanceLog.setFailureReason("礼包配置不存在或无可发放奖励");
                issuanceLog.setUpdateTime(System.currentTimeMillis());
                directRewardIssuanceLogBuilder.update(issuanceLog);

                return IssuePackResponse.builder()
                        .success(false)
                        .packId(request.getPackId())
                        .errorCode("NO_REWARDS_AVAILABLE")
                        .message("礼包配置不存在或无可发放奖励")
                        .transactionId(request.getTransactionId())
                        .isDuplicate(false)
                        .build();
            }

            // 5. 转换奖励格式
            List<IssuePackResponse.IssuedReward> responseRewards = serviceRewards.stream()
                    .map(this::convertToResponseReward)
                    .collect(Collectors.toList());

            // 6. 更新流水状态为成功
            issuanceLog.setIssueStatus("SUCCESS");
            issuanceLog.setRewardsContent(JSON.toJSONString(responseRewards));
            issuanceLog.setUpdateTime(System.currentTimeMillis());
            directRewardIssuanceLogBuilder.update(issuanceLog);

            log.info("礼包发放成功: userId={}, packId={}, 奖励数量={}",
                    request.getUserId(), request.getPackId(), responseRewards.size());

            return IssuePackResponse.builder()
                    .success(true)
                    .packId(request.getPackId())
                    .issuedRewards(responseRewards)
                    .message("礼包发放成功")
                    .issueTime(System.currentTimeMillis())
                    .transactionId(request.getTransactionId())
                    .isDuplicate(false)
                    .build();

        } catch (Exception e) {
            log.error("发放礼包异常: userId={}, packId={}, transactionId={}",
                    request.getUserId(), request.getPackId(), request.getTransactionId(), e);

            // 更新流水状态为失败
            try {
                DirectRewardIssuanceLog failedLog = directRewardIssuanceLogBuilder
                        .findByTransactionId(request.getTransactionId());
                if (failedLog != null) {
                    failedLog.setIssueStatus("FAILED");
                    failedLog.setFailureReason("系统异常: " + e.getMessage());
                    failedLog.setUpdateTime(System.currentTimeMillis());
                    directRewardIssuanceLogBuilder.update(failedLog);
                }
            } catch (Exception updateEx) {
                log.error("更新失败流水异常", updateEx);
            }

            return IssuePackResponse.builder()
                    .success(false)
                    .packId(request.getPackId())
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .transactionId(request.getTransactionId())
                    .isDuplicate(false)
                    .build();
        }
    }

    @Override
    public IssueDirectResponse issueDirect(IssueDirectRequest request) {
        log.info("直接发放奖励: userId={}, channel={}, transactionId={}, 奖励数量={}",
                request.getUserId(), request.getChannel(), request.getTransactionId(),
                request.getRewards() != null ? request.getRewards().size() : 0);

        try {
            // 1. 参数验证
            if (!validateIssueDirectRequest(request)) {
                return IssueDirectResponse.builder()
                        .success(false)
                        .errorCode("INVALID_PARAMS")
                        .message("请求参数不完整")
                        .build();
            }

            // 2. 幂等性检查 - 根据transactionId查询是否已发放
            DirectRewardIssuanceLog existingLog = directRewardIssuanceLogBuilder
                    .findByTransactionId(request.getTransactionId());

            if (existingLog != null) {
                log.info("检测到重复请求，返回已发放的结果: transactionId={}, status={}",
                        request.getTransactionId(), existingLog.getIssueStatus());

                if ("SUCCESS".equals(existingLog.getIssueStatus())) {
                    // 解析已发放的奖励
                    List<IssueDirectResponse.IssuedReward> issuedRewards = parseDirectIssuedRewardsFromLog(existingLog.getRewardsContent());

                    return IssueDirectResponse.builder()
                            .success(true)
                            .issuedRewards(issuedRewards)
                            .message("奖励已发放")
                            .issueTime(existingLog.getCreateTime())
                            .transactionId(request.getTransactionId())
                            .isDuplicate(true)
                            .build();
                } else {
                    return IssueDirectResponse.builder()
                            .success(false)
                            .errorCode("PREVIOUS_FAILED")
                            .message("之前的发放请求失败: " + existingLog.getFailureReason())
                            .transactionId(request.getTransactionId())
                            .isDuplicate(true)
                            .build();
                }
            }

            // 3. 记录发放流水（处理中状态）
            DirectRewardIssuanceLog issuanceLog = createDirectIssuanceLog(request, "PROCESSING", null, null);
            directRewardIssuanceLogBuilder.insert(issuanceLog);

            // 4. 处理每个奖励项
            List<IssueDirectResponse.IssuedReward> responseRewards = new ArrayList<>();
            boolean hasFailure = false;

            for (IssueDirectRequest.RewardItem rewardItem : request.getRewards()) {
                try {
                    // TODO
                    IssueDirectResponse.IssuedReward issuedReward = processDirectRewardItem(rewardItem, request);
                    responseRewards.add(issuedReward);

                    log.info("奖励发放成功: itemId={}, itemType={}, quantity={}",
                            rewardItem.getItemId(), rewardItem.getItemType(), rewardItem.getQuantity());

                } catch (Exception e) {
                    log.error("单个奖励发放失败: itemId={}, itemType={}",
                            rewardItem.getItemId(), rewardItem.getItemType(), e);

                    // 创建失败的奖励记录
                    IssueDirectResponse.IssuedReward failedReward = IssueDirectResponse.IssuedReward.builder()
                            .itemId(rewardItem.getItemId())
                            .itemType(rewardItem.getItemType())
                            .itemName(rewardItem.getItemName())
                            .quantity(rewardItem.getQuantity())
                            .description(rewardItem.getDescription())
                            .iconUrl(rewardItem.getIconUrl())
                            .issueStatus("FAILED")
                            .failureReason("发放异常: " + e.getMessage())
                            .build();

                    responseRewards.add(failedReward);
                    hasFailure = true;
                }
            }

            // 5. 更新流水状态
            String finalStatus = hasFailure ? "PARTIAL_SUCCESS" : "SUCCESS";
            issuanceLog.setIssueStatus(finalStatus);
            issuanceLog.setRewardsContent(JSON.toJSONString(responseRewards));
            issuanceLog.setUpdateTime(System.currentTimeMillis());
            directRewardIssuanceLogBuilder.update(issuanceLog);

            log.info("直接奖励发放完成: userId={}, 总奖励数量={}, 成功数量={}, 失败数量={}",
                    request.getUserId(), responseRewards.size(),
                    responseRewards.stream().mapToInt(r -> "SUCCESS".equals(r.getIssueStatus()) ? 1 : 0).sum(),
                    responseRewards.stream().mapToInt(r -> "FAILED".equals(r.getIssueStatus()) ? 1 : 0).sum());

            return IssueDirectResponse.builder()
                    .success(!hasFailure) // 只有全部成功才返回true
                    .issuedRewards(responseRewards)
                    .message(hasFailure ? "部分奖励发放失败" : "奖励发放成功")
                    .issueTime(System.currentTimeMillis())
                    .transactionId(request.getTransactionId())
                    .isDuplicate(false)
                    .build();

        } catch (Exception e) {
            log.error("直接发放奖励异常: userId={}, transactionId={}",
                    request.getUserId(), request.getTransactionId(), e);

            // 更新流水状态为失败
            try {
                DirectRewardIssuanceLog failedLog = directRewardIssuanceLogBuilder
                        .findByTransactionId(request.getTransactionId());
                if (failedLog != null) {
                    failedLog.setIssueStatus("FAILED");
                    failedLog.setFailureReason("系统异常: " + e.getMessage());
                    failedLog.setUpdateTime(System.currentTimeMillis());
                    directRewardIssuanceLogBuilder.update(failedLog);
                }
            } catch (Exception updateEx) {
                log.error("更新失败流水异常", updateEx);
            }

            return IssueDirectResponse.builder()
                    .success(false)
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .transactionId(request.getTransactionId())
                    .isDuplicate(false)
                    .build();
        }
    }

    // ==================== 统一领奖系统API ====================

    @Override
    public UnifiedClaimResponse claimReward(UnifiedClaimRequest request) {
        log.info("统一领奖: userId={}, claimId={}", request.getUserId(), request.getClaimId());

        try {
            UnifiedClaimService.UnifiedClaimResult
                result = unifiedClaimService.claimReward(request.getUserId(), request.getClaimId());

            if (result.isSuccess()) {
                // 转换奖励信息
                List<UnifiedClaimResponse.RewardItem> rewardItems = result.getRewards().stream()
                        .map(reward -> UnifiedClaimResponse.RewardItem.builder()
                                .itemId(reward.getItemId())
                                .itemName(reward.getItemName())
                                .itemType(reward.getItemType())
                                .quantity(reward.getQuantity())
                                .description(reward.getDescription())
                                .build())
                        .collect(Collectors.toList());

                return UnifiedClaimResponse.builder()
                        .success(true)
                        .claimId(request.getClaimId())
                        .rewards(rewardItems)
                        .claimTime(System.currentTimeMillis())
                        .message(result.getMessage())
                        .build();
            } else {
                return UnifiedClaimResponse.builder()
                        .success(false)
                        .claimId(request.getClaimId())
                        .errorCode(result.getErrorCode())
                        .message(result.getMessage())
                        .build();
            }

        } catch (Exception e) {
            log.error("统一领奖异常", e);
            return UnifiedClaimResponse.builder()
                    .success(false)
                    .claimId(request.getClaimId())
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }
    // ==================== getLotteryState 辅助方法 ====================

    /**
     * 构建用户基本信息
     */
    private LotteryStateResponse.UserProfileInfo buildUserProfileInfo(String saasId, String userId, String prizePoolCode) {
        try {
            // 从用户偏好服务获取用户基本信息
            Map<String, String> allUserPreferences = userPreferenceService.getAllUserPreferences(userId, prizePoolCode);

            //计算当前的周期天数、当前值和结束时间
            UserProgressTracker userProgressTracker = userProgressTrackerBuilder.findByUserIdAndPrizePoolCode(userId, prizePoolCode);
            PrizePool prizePool = prizePoolBuilder.findByCodeAndSaasId(prizePoolCode, saasId);
            LotteryStateResponse.ProgressInfo.ProgressInfoBuilder progressBuilder = LotteryStateResponse.ProgressInfo.builder();
            LotteryStateResponse.ProgressInfo progressInfo = null;
            if (prizePool != null) {
                if (userProgressTracker == null) {
                    userProgressTracker = new UserProgressTracker();
                    userProgressTracker.setUserId(userId);
                    userProgressTracker.setPrizePoolCode(prizePoolCode);
                    userProgressTracker.setCurrentProgress(0);
                    userProgressTracker.setCycleStartTime(OffsetDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.DAYS).toInstant().toEpochMilli());
                    userProgressTrackerBuilder.insert(userProgressTracker);
                }
                long cycleStartTime = userProgressTracker.getCycleStartTime();
                long cycleDuration = TimeUnit.DAYS.toMillis(prizePool.getChestCycleDays());
                long endTime = cycleStartTime + cycleDuration;

                progressInfo =
                    progressBuilder
                        .cycleStartTime(cycleStartTime)
                        .cycleDurationDays(String.valueOf(prizePool.getChestCycleDays()))
                        .cycleEndTime(endTime)
                        .currentValue(userProgressTracker.getCurrentProgress())
                        .build();
            }

            return LotteryStateResponse.UserProfileInfo.builder().preferences(allUserPreferences).progress(progressInfo)
                .build();

        } catch (Exception e) {
            log.error("构建用户基本信息异常: userId={}", userId, e);
            return null;
        }
    }


    /**
     * 构建用户偏好设置
     */
    private LotteryStateResponse.PrizePoolInfo buildPrizePoolInfo(String prizePoolCode, String saasId) {
        try {
            PrizePool prizePool = prizePoolBuilder.findByCodeAndSaasId(prizePoolCode, saasId);
            if (prizePool == null) {
                return null;
            }
            //从prizePool中获取兑换规则
            List<LotteryStateResponse.ExchangeRule> exchangeRules = new ArrayList<>();
            if (StringUtils.hasText(prizePool.getExchangeRules())) {
                exchangeRules = JSON.parseArray(prizePool.getExchangeRules(), LotteryStateResponse.ExchangeRule.class);
            }

            // 从奖品配置中获取奖品信息
            List<PrizeConfig> activeByPrizePoolCode =
                prizeConfigBuilder.findActiveByPrizePoolCode(saasId, prizePoolCode);
            List<LotteryStateResponse.PrizeInfo> prizeInfos = activeByPrizePoolCode.stream()
                .map(prizeConfig -> LotteryStateResponse.PrizeInfo.builder()
                        .rewardId(prizeConfig.getRewardItemId())
                        .rewardName(prizeConfig.getRewardName())
                        .rewardIcon(prizeConfig.getRewardIcon())
                        .quantity(prizeConfig.getRewardQuantity())
                        .build())
                .collect(Collectors.toList());

            return LotteryStateResponse.PrizePoolInfo.builder()
                    .code(prizePool.getCode())
                    .name(prizePool.getName())
                    .exchangeRules(exchangeRules)
                    .prizes(prizeInfos)
                    .build();

        } catch (Exception e) {
            log.error("构建奖池信息异常: prizePoolCode={}", prizePoolCode, e);
            return null;

        }
    }


/**
 * 构建进度宝箱信息，当前奖池关联的所有的宝箱信息，包括icon、解锁进度、名称、id、状态、claimId
 */
    private List<LotteryStateResponse.ChestInfo> buildChestInfo(String saasId, String userId, String prizePoolCode) {
        try {
            List<ProgressChestConfig> chestConfigs = progressChestConfigBuilder.findBySaasIdAndPrizePoolCode(saasId, prizePoolCode);

            // 获取用户进度记录
            UserProgressTracker progressTracker = userProgressTrackerBuilder.findByUserIdAndPrizePoolCode(userId, prizePoolCode);
            long currentProgress = progressTracker != null ? progressTracker.getCurrentProgress() : 0;

            List<LotteryStateResponse.ChestInfo> chestInfos = chestConfigs.stream()
                .map(chestConfig -> {
                    // 查询该宝箱的领取状态
                    UserClaimEntitlement entitlement = userClaimEntitlementBuilder.findUnClaimedChest(
                        userId,
                        chestConfig.getChestId(),
                        progressTracker != null ? progressTracker.getCycleStartTime() : System.currentTimeMillis()
                    );

                    // 判断解锁状态
                    boolean isUnlocked = currentProgress >= chestConfig.getUnlockProgress();
                    String state = entitlement != null ? ActivityConstant.EntitlementStatusEnum.UNCLAIMED.name() : (isUnlocked ? ActivityConstant.EntitlementStatusEnum.CLAIMED.name() : ActivityConstant.EntitlementStatusEnum.LOCKED.name());

                    return LotteryStateResponse.ChestInfo.builder()
                        .chestId(chestConfig.getChestId())
                        .chestName(chestConfig.getChestName())
                        .chestIcon(chestConfig.getIconUrl())
                        .unlockProgress(chestConfig.getUnlockProgress())
                        .state(state)
                        .claimId(entitlement != null ? entitlement.getClaimId() : null)
                        .build();
                })
                .collect(Collectors.toList());

            return chestInfos;

        } catch (Exception e) {
            log.error("构建宝箱信息异常: userId={}, prizePoolCode={}", userId, prizePoolCode, e);
            return null;
        }
    }

    /**
     * 构建可领取奖励信息
     */
    private List<LotteryStateResponse.ClaimableRewardInfo> buildClaimableRewardInfo(String userId, String saasId) {
        List<LotteryStateResponse.ClaimableRewardInfo> claimableRewards = new ArrayList<>();

        try {
            List<UnifiedClaimService.ClaimableReward> rewards = unifiedClaimService.getClaimableRewards(userId);

            for (UnifiedClaimService.ClaimableReward reward : rewards) {
                LotteryStateResponse.ClaimableRewardInfo rewardInfo = LotteryStateResponse.ClaimableRewardInfo.builder()
                        .claimId(reward.getClaimId())
                        .sceneCode(reward.getSceneCode())
                        .rewardType(reward.getRewardType())
                        .rewardName(reward.getRewardName())
                        .rewardIcon(reward.getRewardIcon())
                        .build();
                claimableRewards.add(rewardInfo);
            }
        } catch (Exception e) {
            log.error("构建可领取奖励信息异常: userId={}", userId, e);
        }

        return claimableRewards;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 验证GrantPackRequest参数
     */
    private boolean validateGrantPackRequest(GrantPackRequest request) {
        return request != null
                && StringUtils.hasText(request.getUserId())
                && StringUtils.hasText(request.getSaasId())
                && Objects.nonNull(request.getRewardType())
                && StringUtils.hasText(request.getRewardSourceId())
                && StringUtils.hasText(request.getSourceChannel())
                && StringUtils.hasText(request.getSourceTransactionId());
    }

    /**
     * 验证IssuePackRequest参数
     */
    private boolean validateIssuePackRequest(IssuePackRequest request) {
        return request != null
                && StringUtils.hasText(request.getUserId())
                && StringUtils.hasText(request.getSaasId())
                && StringUtils.hasText(request.getPackId())
                && StringUtils.hasText(request.getChannel())
                && StringUtils.hasText(request.getTransactionId());
    }

    /**
     * 验证IssueDirectRequest参数
     */
    private boolean validateIssueDirectRequest(IssueDirectRequest request) {
        return request != null
                && StringUtils.hasText(request.getUserId())
                && StringUtils.hasText(request.getSaasId())
                && request.getRewards() != null
                && !request.getRewards().isEmpty()
                && StringUtils.hasText(request.getChannel())
                && StringUtils.hasText(request.getTransactionId());
    }

    /**
     * 创建发放流水记录
     */
    private DirectRewardIssuanceLog createIssuanceLog(IssuePackRequest request, String status,
                                                     String rewardsContent, String failureReason) {
        DirectRewardIssuanceLog log = new DirectRewardIssuanceLog();
        log.setTransactionId(request.getTransactionId());
        log.setUserId(request.getUserId());
        log.setSaasId(request.getSaasId());
        log.setChannel(request.getChannel());
        log.setIssueStatus(status);
        log.setDescription(request.getDescription());
        log.setRewardsContent(rewardsContent);
        log.setFailureReason(failureReason);
        log.setClientIp(request.getClientIp());
        log.setDeviceInfo(request.getDeviceInfo());
        log.setCreateTime(System.currentTimeMillis());
        log.setUpdateTime(System.currentTimeMillis());
        return log;
    }

    /**
     * 创建直接发放流水记录
     */
    private DirectRewardIssuanceLog createDirectIssuanceLog(IssueDirectRequest request, String status,
                                                           String rewardsContent, String failureReason) {
        DirectRewardIssuanceLog log = new DirectRewardIssuanceLog();
        log.setTransactionId(request.getTransactionId());
        log.setUserId(request.getUserId());
        log.setSaasId(request.getSaasId());
        log.setChannel(request.getChannel());
        log.setIssueStatus(status);
        log.setDescription(request.getDescription());
        log.setRewardsContent(rewardsContent);
        log.setFailureReason(failureReason);
        log.setClientIp(request.getClientIp());
        log.setDeviceInfo(request.getDeviceInfo());
        log.setCreateTime(System.currentTimeMillis());
        log.setUpdateTime(System.currentTimeMillis());
        return log;
    }

    /**
     * 转换RewardIssueService的奖励为响应格式
     */
    private IssuePackResponse.IssuedReward convertToResponseReward(RewardIssueService.IssuedReward serviceReward) {
        return IssuePackResponse.IssuedReward.builder()
                .itemId(serviceReward.getItemId())
                .itemType(serviceReward.getItemType())
                .itemName(serviceReward.getItemName())
                .quantity(serviceReward.getQuantity())
                .description(serviceReward.getDescription())
                .iconUrl(serviceReward.getIconUrl())
                .build();
    }

    /**
     * 从流水记录解析已发放的奖励（礼包）
     */
    private List<IssuePackResponse.IssuedReward> parseIssuedRewardsFromLog(String rewardsContent) {
        try {
            if (StringUtils.hasText(rewardsContent)) {
                return JSON.parseArray(rewardsContent, IssuePackResponse.IssuedReward.class);
            }
        } catch (Exception e) {
            log.error("解析奖励内容失败: {}", rewardsContent, e);
        }
        return new ArrayList<>();
    }

    /**
     * 从流水记录解析已发放的奖励（直接发放）
     */
    private List<IssueDirectResponse.IssuedReward> parseDirectIssuedRewardsFromLog(String rewardsContent) {
        try {
            if (StringUtils.hasText(rewardsContent)) {
                return JSON.parseArray(rewardsContent, IssueDirectResponse.IssuedReward.class);
            }
        } catch (Exception e) {
            log.error("解析奖励内容失败: {}", rewardsContent, e);
        }
        return new ArrayList<>();
    }

    /**
     * 处理单个直接奖励项
     */
    private IssueDirectResponse.IssuedReward processDirectRewardItem(IssueDirectRequest.RewardItem rewardItem,
                                                                   IssueDirectRequest request) {
        // 这里应该根据itemType调用不同的发放逻辑
        // 例如：ITEM类型调用游戏服务，CURRENCY类型调用资产中心等
        // 目前先模拟成功发放

        log.info("处理直接奖励: itemId={}, itemType={}, quantity={}",
                rewardItem.getItemId(), rewardItem.getItemType(), rewardItem.getQuantity());

        // TODO: 根据itemType实现具体的发放逻辑
        // switch (rewardItem.getItemType()) {
        //     case "ITEM":
        //         // 调用游戏服务发放道具
        //         break;
        //     case "CURRENCY":
        //         // 调用资产中心发放货币
        //         break;
        //     case "POINTS":
        //         // 调用积分服务发放积分
        //         break;
        //     default:
        //         throw new IllegalArgumentException("不支持的奖励类型: " + rewardItem.getItemType());
        // }

        return IssueDirectResponse.IssuedReward.builder()
                .itemId(rewardItem.getItemId())
                .itemType(rewardItem.getItemType())
                .itemName(rewardItem.getItemName())
                .quantity(rewardItem.getQuantity())
                .description(rewardItem.getDescription())
                .iconUrl(rewardItem.getIconUrl())
                .issueStatus("SUCCESS")
                .failureReason(null)
                .build();
    }

    // ==================== 抽奖重构相关辅助方法 ====================

    /**
     * 批量生成历史记录
     */
    private List<DrawHistory> generateBatchHistoryRecords(List<PrizeConfig> prizes,
                                                         DrawBatchRequest request,
                                                         String batchTransactionId,
                                                         ExchangeTicketsDTO exchangeTicketsDTO) {
        List<DrawHistory> records = new ArrayList<>();
        long currentTime = System.currentTimeMillis();

        for (int i = 0; i < prizes.size(); i++) {
            PrizeConfig prize = prizes.get(i);
            String eventId = "evt-" + UUID.randomUUID();

            DrawHistory drawHistory = new DrawHistory();
            drawHistory.setUserId(request.getUserId());
            drawHistory.setEventId(eventId);
            drawHistory.setUpstream_transaction_id(exchangeTicketsDTO.getBusinessId());
            drawHistory.setBatchTransactionId(batchTransactionId);
            drawHistory.setPrizePoolCode(request.getPrizePoolCode());
            drawHistory.setPrizeId(prize.getPrizeId());
            drawHistory.setDrawTime(currentTime);
            drawHistory.setPrizeType(prize.getRewardType());
            drawHistory.setPrizeQuantity(prize.getRewardQuantity());
            drawHistory.setPrizeItemId(prize.getRewardItemId());
            drawHistory.setPrizeItemName(prize.getRewardName());
            drawHistory.setSaasId(request.getSaasId());

            records.add(drawHistory);
        }

        return records;
    }

    /**
     * 汇总抽奖结果
     */
    private List<DrawBatchResponse.DrawResult> aggregateDrawResults(List<PrizeConfig> prizes) {
        Map<String, DrawBatchResponse.DrawResult> resultMap = new HashMap<>();

        for (PrizeConfig prize : prizes) {
            String itemId = prize.getRewardItemId();
            DrawBatchResponse.DrawResult existingResult = resultMap.get(itemId);

            if (existingResult == null) {
                existingResult = DrawBatchResponse.DrawResult.builder()
                    .prizeId(prize.getRewardItemId())
                    .prizeName(prize.getRewardName())
                    .quantity(prize.getRewardQuantity())
                    .prizeType(prize.getRewardType())
                    .build();
                resultMap.put(itemId, existingResult);
            } else {
                existingResult.setQuantity(existingResult.getQuantity() + prize.getRewardQuantity());
            }
        }

        return new ArrayList<>(resultMap.values());
    }

    // 私有辅助方法已移至专门的服务类中
}

