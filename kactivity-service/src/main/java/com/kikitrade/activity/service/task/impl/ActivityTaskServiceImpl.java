package com.kikitrade.activity.service.task.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.openservices.tablestore.TableStoreException;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.api.model.request.TaskListRequest;
import com.kikitrade.activity.api.model.response.*;
import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.mysql.model.CustomerSeqRuleBuilder;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityTaskItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.domain.GroupAward;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.model.util.TaskUtil;
import com.kikitrade.activity.model.util.TimeFormat;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.config.TaskCodeConfig;
import com.kikitrade.activity.service.mq.ActivityEventMessage;
import com.kikitrade.activity.service.task.ActivityTaskService;
import com.kikitrade.activity.service.task.ActivityTaskTccService;
import com.kikitrade.activity.service.task.TaskConfigService;
import com.kikitrade.activity.service.task.domain.TaskCycleDomain;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.redis.lock.RedisDistributedLock;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivityTaskServiceImpl implements ActivityTaskService {

    private static final String TARGET_HANDLE = "targetHandle";

    @Resource
    private RedisService redisService;
    @Resource
    private ActivityTaskItemBuilder activityTaskItemBuilder;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private SeqGeneraterService seqGeneraterService;
    @Resource
    private ActivityTaskTccService activityTaskTccService;
    @Resource
    private TaskConfigService taskConfigService;
    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;
    @Resource
    private RedisDistributedLock redisDistributedLock;

    private static final String TASK_CODE_PREFIX = "TASK_CODE_";

    /**
     * @param request
     * @return
     */
    @Override
    public List<TaskListResponse> taskList(TaskListRequest request) {
        log.info("taskList request:{}", JSON.toJSONString(request));
        boolean taskWhiteFlag = StringUtils.isNotBlank(request.getCustomerId()) && redisService.hGet(RedisKeyConst.TASK_WHITELIST.getMiddleKey(null), request.getCustomerId()) != null;
        ActivityConstant.VipLevelEnum vipLevelEnum = request.getVipLevel() == null ? ActivityConstant.VipLevelEnum.NORMAL : ActivityConstant.VipLevelEnum.valueOf(request.getVipLevel());
        List<TaskConfigDTO> configDTOS = taskConfigService.findTaskBySaasId(request.getSaasId(), request.getChannel(), request.getPosition(), request.getClientType(), taskWhiteFlag);
        List<TaskListResponse> responses = new ArrayList<>();
        if (CollectionUtils.isEmpty(configDTOS)) {
            return responses;
        }
        Map<String, List<String>> cycleMap = new HashMap<>();
        Map<String, List<String>> postCycleMap = new HashMap<>();
        for (TaskConfigDTO dto : configDTOS) {
            if (!isValidVipLevel(dto, vipLevelEnum)) {
                continue;
            }
            TaskListResponse response = createTaskListResponse(dto, request, vipLevelEnum);
            updateCycleMaps(cycleMap, postCycleMap, response);
            handleAppLinks(dto, response);
            handleTaskStatus(dto, request, response, vipLevelEnum);
            responses.add(response);
        }
        buildTaskStatus(responses, cycleMap, request.getCustomerId());
        buildPostTaskStatus(responses, postCycleMap, request.getCustomerId());
        responses = filterCompleted(responses);
        return responses.stream().sorted(Comparator.comparingInt(TaskListResponse::getOrder)).collect(Collectors.toList());
    }

    private List<TaskListResponse> filterCompleted(List<TaskListResponse> responses){
        List<TaskListResponse> res = new  ArrayList<>();
        for(TaskListResponse task : responses){
            if(task.getHiddenTaskCompleted() && task.getStatus().equals(ActivityConstant.TaskCompleteStatus.DONE.getStatus())){
                continue;
            }
            res.add(task);
        }
        return res;
    }

    private boolean isValidVipLevel(TaskConfigDTO dto, ActivityConstant.VipLevelEnum vipLevelEnum) {
        if (!dto.getVipLevel().startsWith("+") && Integer.parseInt(dto.getVipLevel()) != vipLevelEnum.getLevel()) {
            return false;
        }
        return !dto.getVipLevel().startsWith("+") || Integer.parseInt(dto.getVipLevel()) <= vipLevelEnum.getLevel();
    }

    private TaskListResponse createTaskListResponse(TaskConfigDTO dto, TaskListRequest request, ActivityConstant.VipLevelEnum vipLevelEnum) {
        TaskListResponse response = BeanUtil.copyProperties(dto, TaskListResponse.class);
        response.setTitle(parseTitle(dto.getTitle(), request.getChannel()));
        response.setDesc(parseDesc(dto.getDesc(), vipLevelEnum, request.getChannel()));
        response.setLimit(dto.getLimit(vipLevelEnum.name()));
        response.setCycle(TaskCycleDomain.getCurrencyCycle(dto, null));
        response.setCycleEnum(dto.getCycle());
        response.setEndTime(dto.getEndTime());
        String code = dto.getShowCode() == null ? dto.getCode() : dto.getShowCode();
        response.setPostTaskId(dto.getPostTaskId());
        response.setPostTaskCode(dto.getPostTaskCode());
        response.setPostTaskDesc(dto.getPostTaskDesc());
        response.setPostTaskReward(dto.getPostTaskReward());
        if (dto.getReward() != null) {
            List<Award> awards = dto.getReward().getAwards();
            Award award = awards.stream().filter(r -> r.getVipLevel().equals(vipLevelEnum.name())).findFirst().get();
            response.setShowReward(NumberUtils.isDigits(award.getAmount()) ? new BigDecimal(award.getAmount()) : (award.getShowAmount() != null ? new BigDecimal(award.getShowAmount()) : BigDecimal.ZERO));
        }
        if (dto.getShowCode() != null) {
            response.setCode(dto.getShowCode());
        }
        if ("pc".equals(request.getChannel())) {
            response.setConnectUrl(dto.getConnectUrlPc());
        } else {
            response.setConnectUrl(dto.getConnectUrl());
        }
        response.setHiddenTaskCompleted(BooleanUtils.isTrue(dto.getHiddenTaskCompleted()));
        return response;
    }

    private void updateCycleMaps(Map<String, List<String>> cycleMap, Map<String, List<String>> postCycleMap, TaskListResponse response) {
        cycleMap.computeIfAbsent(response.getCycle(), k -> new ArrayList<>()).add(response.getTaskId());
        if (response.getPostTaskId() != null) {
            postCycleMap.computeIfAbsent(response.getCycle(), k -> new ArrayList<>()).add(response.getPostTaskId());
        }
    }

    private void handleAppLinks(TaskConfigDTO dto, TaskListResponse response) {
        Map<String, String> link = response.getLink();
        if (link != null) {
            for (Map.Entry<String, String> entry : link.entrySet()) {
                if (entry.getValue().startsWith("https://twitter.com/compose/post")) {
                    String text = dto.getAttr().get("twitter-random-text");
                    String appendText = dto.getAttr().get("twitter-random-append-text");
                    if (StringUtils.isNotBlank(text)) {
                        List<String> textList = JSON.parseArray(text, String.class);
                        link.put(entry.getKey(), entry.getValue().substring(0, entry.getValue().indexOf("?text=") + 6) + textList.get(RandomUtil.randomInt(textList.size())));
                    } else if (StringUtils.isNotBlank(appendText)) {
                        List<String> textList = JSON.parseArray(appendText, String.class);
                        link.put(entry.getKey(), entry.getValue() + "%0A" + textList.get(RandomUtil.randomInt(textList.size())));
                    }
                }
            }
        }
    }

    private void handleTaskStatus(TaskConfigDTO dto, TaskListRequest request, TaskListResponse response, ActivityConstant.VipLevelEnum vipLevelEnum) {
        if (dto.getGroupId() != null) {
            List<TaskConfigDTO> dtos = taskConfigService.findByGroupTaskId(dto.getGroupId(), false);
            if (CollectionUtils.isNotEmpty(dtos)) {
                Map<String, ActivityTaskItem> taskItemsMap = new HashMap<>();
                if (request.getCustomerId() != null) {
                    List<String> taskIds = dtos.stream().map(TaskConfigDTO::getTaskId).collect(Collectors.toList());
                    List<ActivityTaskItem> taskItems = activityTaskItemBuilder.findByCustomer(request.getCustomerId(), TaskCycleDomain.getCurrencyCycle(dtos.get(0), null), taskIds);
                    log.info("taskList customerId:{}, taskIds:{}, activityItems:{}", request.getCustomerId(), JSON.toJSONString(taskIds), JSON.toJSONString(taskItems));
                    if (CollectionUtils.isNotEmpty(taskItems)) {
                        taskItemsMap = taskItems.stream().collect(Collectors.toMap(ActivityTaskItem::getTaskId, Function.identity(), (v1, v2) -> v2));
                    }
                }
                List<TaskVO> subTasks = new ArrayList<>();
                for (TaskConfigDTO subConfig : dtos) {
                    TaskVO taskVO = createTaskVO(subConfig, taskItemsMap, vipLevelEnum, request);
                    subTasks.add(taskVO);
                }
                response.setSubTasks(subTasks);
            }
        }
        response.setTodayTaskStatus(0);
        if (dto.getCycle() == ActivityTaskConstant.TaskCycleEnum.once_daily) {
            String targetId = TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDD);
            if (request.getCustomerId() != null) {
                ActivityTaskItem taskItem = activityTaskItemBuilder.findDetailByCustomer(request.getCustomerId(),
                    TaskCycleDomain.getCurrencyCycle(dto, null), dto.getTaskId(), targetId);
                if (taskItem != null) {
                    response.setTodayTaskStatus(1);
                }
            }
        }
        response.setStatus(0);
        response.setProgress(0);
    }

    private TaskVO createTaskVO(TaskConfigDTO subConfig, Map<String, ActivityTaskItem> taskItemsMap, ActivityConstant.VipLevelEnum vipLevelEnum, TaskListRequest request) {
        TaskVO taskVO = new TaskVO();
        taskVO.setStatus(0);
        taskVO.setLimit(subConfig.getLimit(vipLevelEnum.name()));
        ActivityTaskItem taskItem = taskItemsMap.get(subConfig.getTaskId());
        if (taskItem != null) {
            String businessId = taskItem.getBusinessId() + taskItem.getProgress() / subConfig.getRewardFrequency();
            ActivityCustomReward reward = activityCustomRewardStoreBuilder.findByBusinessId(taskItem.getCustomerId(), businessId);
            if (reward != null && ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name().equals(reward.getStatus())) {
                taskVO.setRewardStatus(1);
                taskVO.setStatus(1);
            }
        }
        taskVO.setTitle(parseTitle(subConfig.getTitle(), request.getChannel()));
        taskVO.setDesc(parseDesc(subConfig.getDesc(), vipLevelEnum, request.getChannel()));
        taskVO.setTaskId(subConfig.getTaskId());
        return taskVO;
    }

    private void buildTaskStatus(List<TaskListResponse> tasks, Map<String, List<String>> cycleMap, String customerId){
        if(customerId == null){
            return;
        }
        List<ActivityTaskItem> taskItems = new ArrayList<>();
        for(Map.Entry<String, List<String>> entry : cycleMap.entrySet()){
            List<ActivityTaskItem> taskItem = activityTaskItemBuilder.findByCustomer(customerId, entry.getKey(), entry.getValue());
            if(CollectionUtils.isNotEmpty(taskItem)){
                taskItems.addAll(taskItem);
            }
        }
        Map<String, ActivityTaskItem> taskItemMap = taskItems.stream().collect(Collectors.toMap(t -> t.getTaskId(), Function.identity(), (t1, t2) ->
            t1.getCycle().compareTo(t2.getCycle()) > 0 ? t1 : t2));
        for(TaskListResponse task : tasks){
            ActivityTaskItem item = taskItemMap.get(task.getTaskId());
            if (Objects.nonNull(item)) {
                task.setProgress(item.getProgress());
                if (task.getProgressType() == ActivityTaskConstant.ProgressTypeEnum.series) {
                    task.setProgress(item.getProgress() % task.getRewardFrequency());
                }
            }
            task.setStatus(item != null && item.getProgress() >= task.getLimit() ? ActivityConstant.TaskCompleteStatus.DONE.getStatus() : ActivityConstant.TaskCompleteStatus.APPENDING.getStatus());
            if (Objects.nonNull(item) && Objects.nonNull(item.getCompleteTime())) {
                Date completeTime = TimeUtil.parse(item.getCompleteTime(), new SimpleDateFormat(TimeUtil.YYYYMMDDHHMMSS));
                task.setCompleteTime(Objects.nonNull(completeTime) ? completeTime.getTime() : 0L);
            }
        }
    }

    private void buildPostTaskStatus(List<TaskListResponse> tasks, Map<String, List<String>> cycleMap, String customerId){
        if(customerId == null){
            return;
        }
        List<ActivityTaskItem> taskItems = new ArrayList<>();
        for(Map.Entry<String, List<String>> entry : cycleMap.entrySet()){
            List<ActivityTaskItem> taskItem = activityTaskItemBuilder.findByCustomer(customerId, entry.getKey(), entry.getValue());
            if(CollectionUtils.isNotEmpty(taskItem)){
                taskItems.addAll(taskItem);
            }
        }
        Map<String, ActivityTaskItem> taskItemMap = taskItems.stream().collect(Collectors.toMap(t -> t.getTaskId(), Function.identity(), (t1, t2) ->
                t1.getCycle().compareTo(t2.getCycle()) > 0 ? t1 : t2));

        tasks.forEach(task -> {
            ActivityTaskItem item = taskItemMap.get(task.getPostTaskId());
            task.setPostTaskStatus(item != null && item.getProgress() >= task.getLimit() ? ActivityConstant.TaskCompleteStatus.DONE.getStatus() : ActivityConstant.TaskCompleteStatus.APPENDING.getStatus());
        });
    }

    private void buildTaskStatus(TaskListResponse response, TaskConfigDTO dto, String customerId, ActivityConstant.VipLevelEnum vipLevelEnum){
        ActivityTaskItem taskItem = activityTaskItemBuilder.findByCustomer(customerId, TaskCycleDomain.getCurrencyCycle(dto, null), dto.getTaskId());
        response.setProgress(taskItem != null ? taskItem.getProgress() : 0);
        response.setStatus(taskItem != null && taskItem.getProgress() >= dto.getLimit(vipLevelEnum.name()) ? ActivityConstant.TaskCompleteStatus.DONE.getStatus() : ActivityConstant.TaskCompleteStatus.APPENDING.getStatus());
    }

    @Override
    public TaskDetailResponse findByTaskId(String taskId, String customerId, ActivityConstant.VipLevelEnum vipLevelEnum, String channel) {
        TaskConfigDTO taskConfigDTO = taskConfigService.findByTaskIdAndWhiteFlag(taskId, customerId, ActivityTaskConstant.TaskConfigScope.SUB_TASK);
        boolean isGroup = TaskUtil.isGroupTask(taskId);
        if (Objects.isNull(taskConfigDTO)) {
            return null;
        }
        log.info("findByTaskId:{}", JSON.toJSONString(taskConfigDTO));
        boolean taskWhiteFlag = StringUtils.isNotBlank(customerId) && redisService.hGet(RedisKeyConst.TASK_WHITELIST.getMiddleKey(null), customerId) != null;
        //4开头并且子任务不为空
        if (isGroup && !taskConfigDTO.getSubTask().isEmpty()) {
            TaskDetailResponse response = buildByTaskGroupId(taskConfigDTO, customerId, vipLevelEnum, channel,taskWhiteFlag);
            Map<String, String> image = new HashMap<>();
            response.setImage(image);
            return response;
        } else {
            //对于白名单用户做任务，修改任务的开始时间
            taskConfigDTO.setStartTime(fixTaskWhiteDate(taskConfigDTO,taskWhiteFlag));
            TaskDetailResponse response = buildByTaskId(taskConfigDTO, customerId);
            Map<String, String> image = new HashMap<>();
            response.setImage(image);
            return response;
        }
    }

    /**
     * 根据任务 code 查询任务
     *
     * @param taskCode
     * @param customerId
     * @return
     */
    @Override
    public TaskCodeDetailResponse findByTaskCode(String saasId, String taskCode, String customerId) {
        List<TaskConfigDTO> taskConfigDTOS = taskConfigService.findByTaskCodeAndWhiteFlag(saasId, taskCode, customerId);
        if(CollectionUtils.isEmpty(taskConfigDTOS)){
            return null;
        }
        log.info("findByTaskCode response:{}", taskConfigDTOS);
        TaskCodeDetailResponse response = new TaskCodeDetailResponse();
        TaskConfigDTO configDTO = taskConfigDTOS.get(0);
        String cycle = TaskCycleDomain.getCurrencyCycle(configDTO, null);
        TaskCompletedResult taskDetailResult = getTaskResult(customerId, cycle, configDTO.getTaskId());
        response.setTaskId(configDTO.getTaskId());
        response.setTitle(configDTO.getTitle());
        response.setDesc(configDTO.getDesc());
        response.setLabelName(configDTO.getLabelName());
        response.setLabelColor(configDTO.getLabelColor());
        if(MapUtils.isNotEmpty(configDTO.getImage())){
            response.setImage(new ArrayList<>(configDTO.getImage().values()));
        }
        if(taskDetailResult != null && taskDetailResult.getProgress() != null){
            response.setProcess(taskDetailResult.getProgress() % 7 == 0 ? 7 : taskDetailResult.getProgress() % 7);
        }
        ActivityTaskItem todayTask = activityTaskItemBuilder.findDetailByCustomer(customerId, cycle, configDTO.getTaskId(), TimeUtil.getCurrentUtcTime(TimeFormat.YYYYMMDD_PATTERN));
        response.setCheckIn(todayTask != null);
        return response;
    }

    private TaskDetailResponse buildByTaskGroupId(TaskConfigDTO taskConfigDTO, String customerId, ActivityConstant.VipLevelEnum vipLevelEnum, String channel, boolean taskWhiteFlag){
        //白名单用户做任务，返回任务详情时须修改任务开始时间
        taskConfigDTO.setStartTime(fixTaskWhiteDate(taskConfigDTO,taskWhiteFlag));
        TaskDetailResponse response = BeanUtil.copyProperties(taskConfigDTO, TaskDetailResponse.class);
        response.setPlatform(taskConfigDTO.getDomain());
        response.setDesc(parseDesc(taskConfigDTO.getDesc(), vipLevelEnum, channel));
        if(taskConfigDTO.getReward() != null){
            GroupAward groupAward = taskConfigDTO.getReward();
            response.setReward(groupAward);
        }
        List<TaskConfigDTO> subTask = taskConfigDTO.getSubTask();
        List<TaskVO> taskVOS = new ArrayList<>();

        for(TaskConfigDTO taskConfig : subTask){
            TaskVO taskVO = new TaskVO();
            taskVO.setTaskId(taskConfig.getTaskId());
            taskVO.setTitle(parseTitle(taskConfig.getTitle(), channel));
            taskVO.setDesc(parseDesc(taskConfig.getDesc(), vipLevelEnum, channel));
            taskVO.setUrl(taskConfig.getUrl());
            taskVO.setSaasId(taskConfig.getSaasId());
            taskVO.setDomain(taskConfig.getDomain());
            taskVO.setStatus(0);
            taskVO.setCode(taskConfig.getShowCode() != null ? taskConfig.getShowCode() :  taskConfig.getCode());
            taskVO.setPlatform(taskConfig.getDomain());
            taskVO.setStartTime(fixTaskWhiteDate(taskConfig,taskWhiteFlag));
            taskVO.setEndTime(taskConfig.getEndTime());
            taskVO.setAttr(taskConfig.getAttr());
            if (taskConfig.getReward() != null) {
                List<Award> awards = BeanUtil.copyToList(taskConfig.getReward().getAwards(), Award.class);
                taskVO.setRewards(awards);
            }
            if("pc".equals(channel)){
                taskVO.setConnectUrl(taskConfig.getConnectUrlPc());
            }else{
                taskVO.setConnectUrl(taskConfig.getConnectUrl());
            }
            if(customerId != null){
                ActivityTaskItem taskItem = activityTaskItemBuilder.findByCustomer(customerId, TaskCycleDomain.getCurrencyCycle(taskConfig, null), taskConfig.getTaskId());
                taskVO.setStatus((taskItem != null && ActivityConstant.TaskStatusEnum.DONE.name().equals(taskItem.getStatus())) ? 1 : 0);
                taskVO.setRewardStatus(0);
                if(taskItem != null){
                    String businessId = taskItem.getBusinessId() + taskItem.getProgress()/taskConfig.getRewardFrequency();
                    ActivityCustomReward reward = activityCustomRewardStoreBuilder.findByBusinessId(businessId, taskItem.getCustomerId());
                    if(reward != null && ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name().equals(reward.getStatus())){
                        taskVO.setRewardStatus(1);
                    }
                }
            }
            taskVOS.add(taskVO);
        }
        response.setRewardStatus(-1);
        boolean allMatch = taskVOS.stream().allMatch(taskVO -> taskVO.getStatus() == 1);
        if(allMatch){
            response.setRewardStatus(0);
            ActivityTaskItem taskItemAll = activityTaskItemBuilder.findByCustomer(customerId, TaskCycleDomain.getCurrencyCycle(taskConfigDTO, null), taskConfigDTO.getTaskId());
            if(taskItemAll != null){
                String seq = taskItemAll.getEvent() + ":" + taskItemAll.getBusinessId() + taskItemAll.getProgress()/taskConfigDTO.getRewardFrequency();
                ActivityCustomReward reward = activityCustomRewardStoreBuilder.findByPrimaryId(TaskCycleDomain.getCurrencyCycle(taskConfigDTO, null), customerId, seq);
                if(reward != null && ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name().equals(reward.getStatus())){
                    response.setRewardStatus(1);
                }
            }
        }
        response.setStatus(taskVOS.stream().allMatch(t -> t.getStatus() == 1) ? 1 : 0);
        response.setSubTasks(taskVOS);
        response.setTotal(taskVOS.size());
        long count = taskVOS.stream().filter(taskVO -> taskVO.getStatus() == 1).count();
        response.setProcess(count > 0 ? (int)count : 0);
        return response;
    }

    private TaskDetailResponse buildByTaskId(TaskConfigDTO taskConfigDTO, String customerId){
        TaskDetailResponse response = BeanUtil.copyProperties(taskConfigDTO, TaskDetailResponse.class);
        if(taskConfigDTO.getReward() != null){
            response.setReward(taskConfigDTO.getReward());
        }
        response.setSubTasks(new ArrayList<>());
        if(customerId != null){
            ActivityTaskItem taskItem = activityTaskItemBuilder.findByCustomer(customerId, TaskCycleDomain.getCurrencyCycle(taskConfigDTO, null), taskConfigDTO.getTaskId());
            if (taskItem != null) {
                response.setStatus(ActivityConstant.TaskStatusEnum.DONE.name().equals(taskItem.getStatus()) ? 1 : 0);
            } else {
                response.setStatus(0);
            }
        }
        return response;
    }

    /**
     * 白名单人员做任务时，返回给前端的任务开始时间须提前
     * @param taskConfigDTO
     */
    private Long fixTaskWhiteDate(TaskConfigDTO taskConfigDTO, boolean taskWhiteFlag){
        if (taskWhiteFlag) {
            Long startTime = taskConfigDTO.getStartTime();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date(startTime));
            calendar.add(Calendar.DAY_OF_MONTH, -kactivityProperties.getTaskWhiteDate());
            taskConfigDTO.setStartTime(calendar.getTime().getTime());
        }
        return taskConfigDTO.getStartTime();
    }

    /**
     * 做任务
     * @param activityEventMessage
     * @param taskConfig
     * @param codeConfig
     */
    @Override
    public List<Award> doTask(ActivityEventMessage activityEventMessage, TaskConfigDTO taskConfig, TaskCodeConfig codeConfig) throws Exception {
        String distributeKey = RedisKeyConst.TASK_DISTRIBUTE_KEY.getKey("_" + activityEventMessage.getCustomerId() + "_" + taskConfig.getTaskId());
        String vipLevel = activityEventMessage.getBody().get("vipLevel") == null ? ActivityConstant.VipLevelEnum.NORMAL.name()
                : String.valueOf(activityEventMessage.getBody().get("vipLevel"));

        log.info("Starting doTask for customerId: {}, taskId: {}, vipLevel: {}", activityEventMessage.getCustomerId(), taskConfig.getTaskId(), vipLevel);

        ActivityTaskItem taskItem = getTaskItem(activityEventMessage, taskConfig, codeConfig, distributeKey);
        if (taskItem == null) {
            log.warn("Task item is null for customerId: {}, taskId: {}", activityEventMessage.getCustomerId(), taskConfig.getTaskId());
            return null;
        }

        if (ActivityConstant.TaskStatusEnum.FAIL.name().equals(taskItem.getStatus())) {
            if (codeConfig.getInc() < 0) {
                log.warn("Task status is FAIL and codeConfig increment is negative for customerId: {}, taskId: {}", activityEventMessage.getCustomerId(), taskConfig.getTaskId());
                return null;
            }
            taskItem = buildActivityTaskItem(activityEventMessage, taskConfig);
            activityTaskItemBuilder.update(taskItem);
            log.info("Updated task item for customerId: {}, taskId: {}", activityEventMessage.getCustomerId(), taskConfig.getTaskId());
        }

        if (taskItem.getProgress() >= taskConfig.getLimit(vipLevel)) {
            completeTask(taskItem, activityEventMessage);
            log.info("Task completed for customerId: {}, taskId: {}", activityEventMessage.getCustomerId(), taskConfig.getTaskId());
            return null;
        }

        log.info("Executing task for taskItem: {}, activityEventMessage: {}", taskItem, activityEventMessage);
        return executeTask(taskItem, activityEventMessage, taskConfig, codeConfig);
    }

    private ActivityTaskItem getTaskItem(ActivityEventMessage activityEventMessage, TaskConfigDTO taskConfig, TaskCodeConfig codeConfig, String distributeKey) throws Exception {
        try {
            if (!redisDistributedLock.tryLock(distributeKey, Duration.ofSeconds(5))) {
                log.warn("Failed to acquire lock for distributeKey: {}", distributeKey);
                throw new ActivityException(ActivityResponseCode.TASK_RETRY);
            }

            log.info("Lock acquired for distributeKey: {}", distributeKey);

            ActivityTaskItem taskItem = activityTaskItemBuilder.findByCustomer(activityEventMessage.getCustomerId(), TaskCycleDomain.getCurrencyCycle(taskConfig, activityEventMessage.getEventTime()), taskConfig.getTaskId());
            if (taskItem == null && codeConfig.getInc() >= 0) {
                taskItem = buildActivityTaskItem(activityEventMessage, taskConfig);
                if (!activityTaskItemBuilder.insert(taskItem)) {
                    log.warn("Failed to insert task item for distributeKey: {}", distributeKey);
                    throw new ActivityException(ActivityResponseCode.TASK_RETRY);
                }
                log.info("Inserted new task item for customerId: {}, taskId: {}", activityEventMessage.getCustomerId(), taskConfig.getTaskId());
            }
            return taskItem;
        } catch (Exception e) {
            log.error("Error in getTaskItem for distributeKey: {}", distributeKey, e);
            throw new ActivityException(ActivityResponseCode.TASK_RETRY);
        } finally {
            redisDistributedLock.unlock(distributeKey);
            log.info("Lock released for distributeKey: {}", distributeKey);
        }
    }

    private void completeTask(ActivityTaskItem taskItem, ActivityEventMessage activityEventMessage) {
        if (!ActivityConstant.TaskStatusEnum.DONE.name().equals(taskItem.getStatus())) {
            taskItem.setStatus(ActivityConstant.TaskStatusEnum.DONE.name());
            taskItem.setCompleteTime(TimeUtil.getUtcTime(TimeUtil.parseUnittime(activityEventMessage.getEventTime()), TimeUtil.YYYYMMDDHHMMSS));
            activityTaskItemBuilder.update(taskItem);
            log.info("Task marked as complete for taskItem: {}", taskItem);
        }
    }

    private List<Award> executeTask(ActivityTaskItem taskItem, ActivityEventMessage activityEventMessage, TaskConfigDTO taskConfig, TaskCodeConfig codeConfig) throws Exception {
        try {
            log.info("Executing task in TCC service for taskItem: {}", taskItem);
            return activityTaskTccService.doTask(taskItem, activityEventMessage, taskConfig, codeConfig);
        } catch (TableStoreException ex) {
            log.error("Error executing task in TCC service for taskItem: {}", taskItem, ex);
            throw new ActivityException(ActivityResponseCode.TASK_RETRY);
        }
    }

    @Override
    public TaskCompletedResult getTaskResult(String customerId, String cycle, String taskId) {
        TaskCompletedResult result = new TaskCompletedResult();
        ActivityTaskItem taskItem = activityTaskItemBuilder.findByCustomer(customerId, cycle, taskId);
        Page<ActivityTaskItem> list = activityTaskItemBuilder.findByIdList(customerId, taskId, TimeUtil.getDataStr(TimeUtil.addDay(TimeUtil.parse(cycle, new SimpleDateFormat("yyyyMMdd")), -50), new SimpleDateFormat("yyyyMMdd")), cycle, 100);
        int consecutiveDays = taskItem != null && ActivityConstant.TaskStatusEnum.DONE.name().equals(taskItem.getStatus()) ? 1 : 0;
        int complateDays = taskItem != null && ActivityConstant.TaskStatusEnum.DONE.name().equals(taskItem.getStatus()) ? 1 : 0;
        int start = taskItem != null ? 1 : 0;
        if(CollectionUtils.isNotEmpty(list.getRows())){
            List<ActivityTaskItem> sortedItems = list.getRows();
            String currentCycle = TimeUtil.getDataStr(TimeUtil.addDay(TimeUtil.parse(cycle, new SimpleDateFormat("yyyyMMdd")), -1), "yyyyMMdd");
            for(int i = start; i < list.getRows().size(); i++){
                ActivityTaskItem item = sortedItems.get(i);
                if(ActivityConstant.TaskStatusEnum.DONE.name().equals(item.getStatus())) {
                    complateDays++;
                    if(TimeUtil.parse(currentCycle, new SimpleDateFormat("yyyyMMdd")).compareTo(TimeUtil.parse(item.getCycle(), new SimpleDateFormat("yyyyMMdd"))) == 0) {
                        consecutiveDays++;
                        currentCycle = TimeUtil.getDataStr(TimeUtil.addDay(TimeUtil.parse(currentCycle, new SimpleDateFormat("yyyyMMdd")), -1), "yyyyMMdd");
                    }
                }
            }
        }
        if(taskItem == null){
            result.setConsecutiveDays(consecutiveDays);
            result.setCompleteDays(complateDays);
            result.setCustomerId(customerId);
            result.setTaskId(taskId);
            result.setCycle(cycle);
            result.setStatus(ActivityConstant.TaskStatusEnum.NOT_STARTED.name());
            return result;
        }
        TaskCompletedResult taskCompletedResult = BeanUtil.copyProperties(taskItem, TaskCompletedResult.class);
        taskCompletedResult.setConsecutiveDays(consecutiveDays);
        taskCompletedResult.setCompleteDays(complateDays);
        return taskCompletedResult;
    }

    @Override
    public TaskCompletedResult getTaskDetailResult(String customerId, String cycle ,String taskId, String targetId) {
        ActivityTaskItem taskItems = activityTaskItemBuilder.findDetailByCustomer(customerId, cycle, taskId, targetId);
        return taskItems == null ? null : BeanUtil.copyProperties(taskItems, TaskCompletedResult.class);
    }

    @Override
    public TaskCompletedResult getTaskResultBySocial(String platform, String socialCustomerId, String cycle, String taskId) {
        TaskCompletedResult result = new TaskCompletedResult();
        ActivityTaskItem taskItem = activityTaskItemBuilder.findByTarget(platform, socialCustomerId, cycle, taskId);
        if(taskItem == null){
            result.setStatus(ActivityConstant.TaskStatusEnum.NOT_STARTED.name());
            return result;
        }
        return BeanUtil.copyProperties(taskItem, TaskCompletedResult.class);
    }

    @Override
    public List<Award> findByTaskCode(String saasId, String taskCode, ActivityConstant.VipLevelEnum vipLevelEnum) {
        List<TaskConfigDTO> configDTOS = taskConfigService.findByTaskCode(saasId, taskCode);
        List<Award> awards = new ArrayList<>();
        for(TaskConfigDTO configDTO : configDTOS){
            List<Award> award = configDTO.getReward().getAwards();
            awards.add(award.stream().filter(a -> vipLevelEnum.name().equals(a.getVipLevel())).findFirst().get());
        }
        return awards;
    }

    @Override
    public List<Award> listTaskRewards(String saasId, String taskCode, ActivityConstant.VipLevelEnum vipLevelEnum) {
        List<TaskConfigDTO> configDTOS = taskConfigService.findByTaskCode(saasId, taskCode);
        List<Award> awards = new ArrayList<>();
        for(TaskConfigDTO configDTO : configDTOS){
            List<Award> award = configDTO.getReward().getAwards();
            awards.addAll(award.stream().filter(a -> vipLevelEnum.name().equals(a.getVipLevel())).collect(Collectors.toList()));
        }
        return awards;
    }

    /**
     * 查询任务是否完成
     * @param saasId
     * @param taskId
     * @param customerId
     * @param type
     * @return
     */
    @Override
    public TaskProgressResponse findProgressByTaskId(String saasId, String taskId, String customerId, String type) {
        TaskProgressResponse response = new TaskProgressResponse();
        response.setStatus(1);//1：已完成，不弹窗
        boolean taskWhiteFlag = StringUtils.isNotBlank(customerId) && redisService.hGet(RedisKeyConst.TASK_WHITELIST.getMiddleKey(null), customerId) != null;
        TaskConfigDTO configDTO = taskConfigService.findByTaskIdAndWhiteFlag(taskId, customerId, ActivityTaskConstant.TaskConfigScope.SUB_TASK);
        if (Objects.isNull(configDTO)) {
            log.warn("findProgressByTaskId configDTO is null");
            return null;
        }
        List<TaskConfigDTO> subTasks = configDTO.getSubTask().stream().sorted((o1, o2) -> o2.getStartTime().compareTo(o1.getStartTime())).collect(Collectors.toList());
        if ("reward".equals(type)) {
            if (taskDateLimit(configDTO, taskWhiteFlag)) {
                log.info("findProgressByTaskId, time out");
                return response;
            }
            if(configDTO.getAttr() != null && configDTO.getAttr().get("preTaskId") != null){
                List<ActivityTaskItem> taskItems = activityTaskItemBuilder.findByCustomer(customerId, configDTO.getAttr().get("preTaskId"));
                if(CollectionUtils.isEmpty(taskItems)){
                    log.info("findProgressByTaskId, pre task not completed");
                    return response;
                }
            }
            for (TaskConfigDTO dto : subTasks) {
                if (taskDateLimit(dto, taskWhiteFlag)) {
                    log.info("findProgressByTaskId, task time time out");
                    continue;
                }
                String rt = dto.getAttr().get("register-time");
                if(rt != null && customerId.substring(0, rt.length()).compareTo(rt) >= 0){
                    log.info("findProgressByTaskId, register time time out");
                    continue;
                }
                ActivityTaskItem taskItem = activityTaskItemBuilder.findByCustomer(customerId, TaskCycleDomain.getCurrencyCycle(dto, null), dto.getTaskId());
                if(taskItem == null || !"DONE".equals(taskItem.getStatus())){
                    response.setStatus(0);
                    return response;
                }
                String businessId = taskItem.getBusinessId() + taskItem.getProgress() / dto.getRewardFrequency();
                ActivityCustomReward reward = activityCustomRewardStoreBuilder.findByBusinessId(taskItem.getCustomerId(), businessId);
                if (reward == null || !ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name().equals(reward.getStatus())) {
                    response.setStatus(0);
                    return response;
                }
            }
        }
        return response;
    }

    @Override
    public TaskPopResponse getTaskEarlyBirdPop(String saasId, String customerId) {
        TaskPopResponse response = new TaskPopResponse();
        response.setPop(false);//1：已完成，不弹窗
        List<TaskConfigDTO> taskConfigDTOS = taskConfigService.findByTaskCodeAndWhiteFlag(saasId, "early_bird", customerId, ActivityTaskConstant.TaskConfigScope.SUB_TASK);
        if (CollectionUtils.isEmpty(taskConfigDTOS)) {
            log.info("findProgressByTaskId, time out");
            return response;
        }
        taskConfigDTOS.sort((o1, o2) -> o2.getStartTime().compareTo(o1.getStartTime()));
        TaskConfigDTO configDTO = taskConfigDTOS.get(0);
        List<TaskConfigDTO> subTasks =
            configDTO.getSubTask().stream().sorted((o1, o2) -> o2.getStartTime().compareTo(o1.getStartTime()))
                .collect(Collectors.toList());
        if (configDTO.getAttr() != null && configDTO.getAttr().get("preTaskId") != null) {
            List<ActivityTaskItem> taskItems =
                activityTaskItemBuilder.findByCustomer(customerId, configDTO.getAttr().get("preTaskId"));
            if (CollectionUtils.isEmpty(taskItems)) {
                log.info("findProgressByTaskId, pre task not completed");
                return response;
            }
        }
        if (CollectionUtils.isNotEmpty(subTasks)) {
            Map<String, ActivityTaskItem> taskItemsMap = new HashMap<>();
            List<String> taskIds = subTasks.stream().map(e -> e.getTaskId()).collect(Collectors.toList());
            List<ActivityTaskItem> taskItems = activityTaskItemBuilder.findByCustomer(customerId,TaskCycleDomain.getCurrencyCycle(subTasks.get(0), null),taskIds);
            log.info("TaskEarlyBirdPop customerId:{}, taskIds:{}, activityItems:{}", customerId, JSON.toJSONString(taskIds), JSON.toJSONString(taskItems));
            if (CollectionUtils.isNotEmpty(taskItems)) {
                taskItemsMap = taskItems.stream().collect(Collectors.toMap(ActivityTaskItem::getTaskId, Function.identity(), (v1, v2) -> v2));
            }
            boolean taskWhiteFlag = StringUtils.isNotBlank(customerId) && redisService.hGet(RedisKeyConst.TASK_WHITELIST.getMiddleKey(null), customerId) != null;
            for (TaskConfigDTO dto : subTasks) {
                if (taskDateLimit(dto, taskWhiteFlag)) {
                    log.info("findProgressByTaskId, task time time out");
                    continue;
                }
                String rt = dto.getAttr().get("register-time");
                if (rt != null && customerId.substring(0, rt.length()).compareTo(rt) >= 0) {
                    log.info("findProgressByTaskId, register time time out");
                    continue;
                }
                ActivityTaskItem taskItem = taskItemsMap.get(dto.getTaskId());
                if (taskItem == null || !"DONE".equals(taskItem.getStatus())) {
                    response.setPop(true);
                    response.setTaskId(configDTO.getTaskId());
                    return response;
                }
                String businessId = taskItem.getBusinessId() + taskItem.getProgress()/dto.getRewardFrequency();
                ActivityCustomReward reward = activityCustomRewardStoreBuilder.findByBusinessId(taskItem.getCustomerId(), businessId);
                if(reward == null || !ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name().equals(reward.getStatus())){
                    response.setPop(true);
                    response.setTaskId(configDTO.getTaskId());
                    return response;
                }
            }
        }
        return response;
    }

    private void preCheck(TaskConfigDTO configDTO, String customerId) throws ActivityException {
        if (MapUtils.isEmpty(configDTO.getAttr()) || configDTO.getAttr().get("preTaskId") == null) {
            return;
        }
        String[] list = configDTO.getAttr().get("preTaskId").split(",");
        for (String taskId : list) {
            TaskConfigDTO task = taskConfigService.findByTaskIdAndWhiteFlag(taskId, customerId);
            if (Objects.isNull(task)) {
                throw new ActivityException(ActivityResponseCode.INVALID_PARAMETER, taskId);
            }
            TaskCompletedResult taskResult = getTaskResult(customerId, TaskCycleDomain.getCurrencyCycle(task, null), taskId);
            if (!taskResult.isDone()) {
                throw new ActivityException(ActivityResponseCode.TASK_PRE_CHECK_NOT_PASS, taskId);
            }
        }
    }

    private ActivityTaskItem buildActivityTaskItem(ActivityEventMessage activityEventMessage, TaskConfigDTO config){
        ActivityTaskItem taskItem = ActivityTaskItem
                .buildPrimary(activityEventMessage.getCustomerId(),
                        config.getTaskId(), TaskCycleDomain.getCurrencyCycle(config, activityEventMessage.getEventTime()));
        taskItem.setBusinessId(seqGeneraterService.next(CustomerSeqRuleBuilder.instance(activityTaskItemBuilder.getTableName())));
        taskItem.setEvent(config.getCode());
        taskItem.setStatus(ActivityConstant.TaskStatusEnum.APPENDING.name());
        String vipLevel = activityEventMessage.getBody().get("vipLevel") == null ? null
                : String.valueOf(activityEventMessage.getBody().get("vipLevel"));
        taskItem.setCompleteThreshold(config.getLimit(vipLevel));
        taskItem.setProgress(0);
        taskItem.setExpiredTime(TaskCycleDomain.getCurrencyCycleEnd(config, activityEventMessage.getEventTime()));
        return taskItem;
    }

    private String parseTitle(String title, String channel){
        if(title.contains("{")){
            return String.valueOf(JSON.parseObject(title).get(channel.toUpperCase()));
        }else{
            return title;
        }
    }

    private boolean checkExt(String ext, String... fields) {
        if (fields.length == 0) {
            return true;
        }
        if (StringUtils.isBlank(ext) || !JSONUtil.isTypeJSON(ext)) {
            log.error("checkExt fail, ext is null or not invalid , ext:{}", ext);
            return false;
        }
        JSONObject json = JSON.parseObject(ext);
        List<String> missingFields = Arrays.stream(fields).filter(field -> !json.containsKey(field)).collect(Collectors.toList());
        if (missingFields != null && !missingFields.isEmpty()) {
            log.error("checkExt fail, ext not contain fields:{}, ext:{}", missingFields, ext);
            return false;
        }
        return true;
    }

    private boolean taskDateLimit(TaskConfigDTO taskConfig, boolean taskWhiteFlag) {
        log.info("taskDateLimit, taskConfig:{}, taskWhiteFlag:{}", JSON.toJSONString(taskConfig), taskWhiteFlag);
        if (taskWhiteFlag) {
            //配置任务白名单时，可提前查看到任务
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, kactivityProperties.getTaskWhiteDate());
            if (calendar.getTime().getTime() < taskConfig.getStartTime() || new Date().getTime() >= taskConfig.getEndTime()) {
                return true;
            }
        } else {
            if (new Date().getTime() < taskConfig.getStartTime() || new Date().getTime() >= taskConfig.getEndTime()) {
                return true;
            }
        }
        return false;
    }

    private String parseDesc(String desc, ActivityConstant.VipLevelEnum vipLevelEnum, String channel){
        if(desc != null && desc.contains("\n")){
            return desc.split("\\n")[vipLevelEnum.getLevel()];
        }else if(desc != null && desc.contains("{")){
            try{
                return String.valueOf(JSON.parseObject(desc).get(String.format("%s_%s", channel, vipLevelEnum.name()).toUpperCase()));
            }catch (Exception ex){
                return desc;
            }
        }else{
            return desc;
        }
    }
}
