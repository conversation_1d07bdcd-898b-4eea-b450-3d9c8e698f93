package com.kikitrade.activity.service.task.domain;

import cn.hutool.core.util.RandomUtil;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.domain.GroupAward;
import org.apache.commons.collections.MapUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/2/5 18:30
 */
public class TaskAwardDomain {

    public static List<Award> getAward(TaskConfigDTO taskConfigDTO, AtomicLong progress, String vipLevel){
        if(taskConfigDTO.getRewardForm() == ActivityTaskConstant.RewardForm.none || taskConfigDTO.getReward() == null){
            return null;
        }
        GroupAward reward = taskConfigDTO.getReward();
        long index = taskConfigDTO.getProgressType() == ActivityTaskConstant.ProgressTypeEnum.series
            ? (progress.get() % taskConfigDTO.getRewardFrequency() == 0 ? taskConfigDTO.getRewardFrequency() : progress.get() % taskConfigDTO.getRewardFrequency())
            : progress.get() % taskConfigDTO.getRewardFrequency();
        if(index != 0){
            return  null;
        }
        List<Award> awards = reward.getAwards();
        if(awards != null && progress.get() <= taskConfigDTO.getLimit(vipLevel)){
            if(taskConfigDTO.getRewardForm() == ActivityTaskConstant.RewardForm.fixed){
                if(vipLevel != null){
                    return awards.stream().filter(award -> vipLevel.equals(award.getVipLevel())).toList();
                }else{
                    if(awards.size() > 1){
                        return awards.stream().filter(award -> "NORMAL".equals(award.getVipLevel())).toList();
                    }else{
                        Award award = awards.get(0);
                        if(award.getVipLevel() == null || "NORMAL".equals(award.getVipLevel())){
                            return Arrays.asList(award);
                        }
                    }
                }
            }else{
                if(vipLevel != null){
                    List<Award> awardList = awards.stream().filter(award -> vipLevel.equals(award.getVipLevel())).toList();
                    awardList.get(RandomUtil.randomInt(awardList.size()));
                }else{
                    return Arrays.asList(awards.get(RandomUtil.randomInt(awards.size())));
                }
            }
        }
        return null;
    }
}
