package com.kikitrade.activity.service.reward.impl;

import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.service.reward.RewardTccService;
import com.kikitrade.activity.model.domain.RewardRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service("rewardAssetTccService")
public class RewardAssetTccServiceImpl extends AbstractRewardTccService implements RewardTccService {

    /**
     * @param request
     * @return
     * @throws Exception
     */
    @Override
    public void tryReward(RewardRequest request) throws Exception {

    }

    /**
     * 发放奖励
     *
     * @param activityCustomReward
     * @throws Exception
     */
    @Override
    public void reward(ActivityCustomReward activityCustomReward) throws Exception {
    }
}
