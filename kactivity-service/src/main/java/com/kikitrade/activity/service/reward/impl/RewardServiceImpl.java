package com.kikitrade.activity.service.reward.impl;

import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityTaskItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.domain.RewardRequest;
import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.reward.RewardService;
import com.kikitrade.activity.service.reward.RewardTccService;
import com.kikitrade.activity.service.task.TaskConfigService;
import com.kikitrade.activity.service.task.domain.TaskCycleDomain;
import com.mzt.logapi.starter.annotation.LogRecord;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;

import static com.kikitrade.activity.model.util.TimeUtil.YYYYMMDD;

/**
 * OperationRewardService Implementation
 *
 * <AUTHOR>
 * @create 2021/10/25 7:39 下午
 * @modify
 */
@Slf4j
@Component
public class RewardServiceImpl implements RewardService {

    @Resource
    private RewardTccService rewardTccService;
    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;
    @Resource
    private TaskConfigService taskConfigService;
    @Resource
    private ActivityTaskItemBuilder activityTaskItemBuilder;

    @Override
    @LogRecord(success = "【{{#request.customerId}}】reward_success【{{#request.amount}}】【{{#request.currency}}】", type = "ACTIVITY", subType = "REWARD", bizNo = "{{#request.rewardId}}", successCondition = "{{#_ret.code.success}}"
            , fail = "【{{#request.customerId}}】reward_fail【{{#request.amount}}】【{{#request.currency}}】result：{{#_errorMsg}}{{#_ret}}")
    public ActivityResponse reward(RewardRequest request) throws Exception {
        log.info("[reward] reward request:{}", request);
        if (StringUtils.isBlank(request.getCustomerId())
                || request.getAmount() == null) {
            return ActivityResponse.builder()
                    .code(ActivityResponseCode.INVALID_PARAMETER)
                    .msg("customerId or currency or amount is null").build();
        }
        rewardTccService.getService(request.getType()).tryReward(request);
        return ActivityResponse.builder().code(ActivityResponseCode.SUCCESS).msg("success").build();
    }

    /**
     * 查询我带领取的奖励
     *
     * @param rewardRequest
     * @return
     */
    @Override
    public ActivityCustomReward getReward(RewardRequest rewardRequest) {
        return activityCustomRewardStoreBuilder.findOne(rewardRequest);
    }

    /**
     * 领取钱包
     * @param customerId
     * @param taskId
     * @return
     */
    @Override
    public ActivityResponse receiveReward(String customerId, String taskId) {
        log.info("receiveReward request:{}, {}", customerId, taskId);
        //检查任务是否都完成
        TaskConfigDTO taskConfigDTO = taskConfigService.findByTaskIdAndWhiteFlag(taskId, customerId, ActivityTaskConstant.TaskConfigScope.SUB_TASK);
        if (Objects.isNull(taskConfigDTO)) {
            log.warn("receiveReward taskConfigDTO is null");
            return ActivityResponse.builder().code(ActivityResponseCode.INVALID_PARAMETER).build();
        }
        ActivityTaskItem taskItem = activityTaskItemBuilder.findByCustomer(customerId, TaskCycleDomain.getCurrencyCycle(taskConfigDTO, null), taskConfigDTO.getTaskId());
        log.info("receiveReward taskResult:{}", taskItem);
        if(!"DONE".equals(taskItem.getStatus())){
            log.info("receiveReward customerId:{}, cycle:{}, taskId:{}, not done", customerId, TaskCycleDomain.getCurrencyCycle(taskConfigDTO, null), taskConfigDTO.getTaskId());
            return ActivityResponse.builder().code(ActivityResponseCode.TASK_NOT_PASS).build();
        }
        try{
            String businessId = generateBusinessId(taskConfigDTO, taskItem, new AtomicLong(taskItem.getProgress()));
            List<Award> awards = taskConfigDTO.getReward().getAwards();
            for(Award award : awards) {
                String seq = generateSeq(taskItem.getEvent(), businessId, award.getType());
                ActivityCustomReward customReward = activityCustomRewardStoreBuilder.findByPrimaryId(taskItem.getCycle(), customerId, seq);
                rewardTccService.getService(award.getType()).reward(customReward);
            }
            return ActivityResponse.builder().code(ActivityResponseCode.SUCCESS).build();
        }catch (Exception exception){
            log.error("receiveReward error:{}, {}", customerId, taskId, exception);
            return ActivityResponse.builder().code(ActivityResponseCode.SYSTEM_ERROR).build();
        }
    }

    private String generateBusinessId(TaskConfigDTO taskConfig, ActivityTaskItem taskItem, AtomicLong progress) {
        if (taskConfig.getProgressType() == ActivityTaskConstant.ProgressTypeEnum.series) {
            return taskItem.getBusinessId() + TimeUtil.getCurrentUtcTime(YYYYMMDD) + progress.get() / taskConfig.getRewardFrequency();
        } else {
            return taskItem.getBusinessId() + progress.get() / taskConfig.getRewardFrequency();
        }
    }

    private String generateSeq(String event, String businessId, String type) {
        return event + ":" + businessId + ":" + type;
    }
}
