package com.kikitrade.activity.service.draw.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.kikitrade.activity.dal.tablestore.builder.ProgressChestConfigBuilder;
import com.kikitrade.activity.dal.tablestore.builder.RandomRewardPoolBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ItemMasterConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.GiftPackConfig;
import com.kikitrade.activity.dal.tablestore.model.ProgressChestConfig;
import com.kikitrade.activity.dal.tablestore.model.RandomRewardPool;
import com.kikitrade.activity.dal.tablestore.model.ItemMasterConfig;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.domain.PreferenceMapping;
import com.kikitrade.activity.service.draw.RewardIssueService;
import com.kikitrade.activity.service.goods.GiftPackConfigProcessor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;

/**
 * 奖励发放服务实现
 * 严格按照技术规格书要求实现动态奖励组合逻辑
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class RewardIssueServiceImpl implements RewardIssueService {

    @Resource
    private GiftPackConfigProcessor giftPackConfigProcessor;

    @Resource
    private ProgressChestConfigBuilder progressChestConfigBuilder;

    @Resource
    private RandomRewardPoolBuilder randomRewardPoolBuilder;
    
    @Resource
    private ItemMasterConfigBuilder itemMasterConfigBuilder;
    
    @Override
    public List<IssuedReward> issueRewardsByPackId(String packId, String saasId) {
        log.info("开始发放礼包奖励: packId={}, saasId={}", packId, saasId);
        
        List<IssuedReward> issuedRewards = new ArrayList<>();
        
        try {
            // 定义固定物品处理器
            Function<GiftPackConfig, IssuedReward> fixedItemProcessor = this::createIssuedReward;

            // 定义随机池处理器
            Function<GiftPackConfig, List<IssuedReward>> randomPoolProcessor = config ->
                pickFromRandomPool(config.getRandomPoolId(), config.getPickCount(), saasId);

            // 使用通用处理器处理配置
            issuedRewards = giftPackConfigProcessor.processGiftPackConfigs(
                packId, saasId, fixedItemProcessor, randomPoolProcessor);

            
            log.info("礼包奖励发放完成: packId={}, 总奖励数量={}", packId, issuedRewards.size());
            
        } catch (Exception e) {
            log.error("发放礼包奖励异常: packId={}, saasId={}", packId, saasId, e);
        }
        
        return issuedRewards;
    }

    @Override
    public List<IssuedReward> issueRewardsByChestId(String chestId, String saasId) {
        log.info("开始发放宝箱奖励: chestId={}, saasId={}", chestId, saasId);

        List<IssuedReward> issuedRewards = new ArrayList<>();

        try {
            // 1. 根据宝箱ID从 chest_config 表中查询宝箱的基础配置信息
            ProgressChestConfig chestConfig = progressChestConfigBuilder.findByChestId(chestId, saasId);

            if (chestConfig == null) {
                log.warn("未找到宝箱配置: chestId={}, saasId={}", chestId, saasId);
                return issuedRewards;
            }

            return issueRewardsByPackId(chestConfig.getPackIdOnUnlock(), saasId);
        } catch (Exception e) {
            log.error("发放宝箱奖励异常: chestId={}, saasId={}", chestId, saasId, e);
        }

        return issuedRewards;
    }

    @Override
    public List<IssuedReward> issueRewardsByChestIdWithPreferences(String chestId, String userId, String saasId, Map<String, String> userPreferences) {
        log.info("根据宝箱ID和用户偏好发放奖励: chestId={}, userId={}, saasId={}, userPreferences={}",
                chestId, userId, saasId, userPreferences);

        List<IssuedReward> issuedRewards = new ArrayList<>();

        try {
            // 1. 查询宝箱配置
            ProgressChestConfig chestConfig = progressChestConfigBuilder.findByChestId(chestId, saasId);
            if (chestConfig == null) {
                log.warn("未找到宝箱配置: chestId={}, saasId={}", chestId, saasId);
                return issuedRewards;
            }

            // 2. 根据奖励类型处理
            ActivityConstant.ChestRewardTypeEnum rewardType = ActivityConstant.ChestRewardTypeEnum.fromCode(chestConfig.getRewardType());

            switch (rewardType) {
                case ITEM:
                    return handleItemReward(chestConfig, userPreferences, userId, saasId);
                case GIFT_PACK:
                    return handleGiftPackReward(chestConfig, userPreferences, saasId);
                default:
                    log.warn("未知的奖励类型: {}", chestConfig.getRewardType());
                    return issuedRewards;
            }

        } catch (Exception e) {
            log.error("根据宝箱ID和用户偏好发放奖励异常: chestId={}, userId={}, saasId={}", chestId, userId, saasId, e);
        }

        return issuedRewards;
    }

    @Override
    public IssuedReward issueDirectItem(String itemId, Integer quantity, String userId, String saasId) {
        log.info("直接发放道具: itemId={}, quantity={}, userId={}, saasId={}", itemId, quantity, userId, saasId);

        try {
            // 查询道具主数据
            ItemMasterConfig itemConfig = itemMasterConfigBuilder.findByItemId(itemId, saasId);
            if (itemConfig == null) {
                log.warn("未找到道具配置: itemId={}, saasId={}", itemId, saasId);
                return null;
            }

            // 创建发放结果
            return new IssuedReward(
                itemConfig.getItemId(),
                itemConfig.getItemType(),
                itemConfig.getItemName(),
                quantity,
                itemConfig.getDescription(),
                itemConfig.getItemIcon()
            );

        } catch (Exception e) {
            log.error("直接发放道具异常: itemId={}, quantity={}, userId={}, saasId={}", itemId, quantity, userId, saasId, e);
            return null;
        }
    }

    @Override
    public List<IssuedReward> pickFromRandomPool(String poolId, Integer pickCount, String saasId) {
        log.info("从随机池抽取奖励: poolId={}, pickCount={}, saasId={}", poolId, pickCount, saasId);
        
        List<IssuedReward> pickedRewards = new ArrayList<>();
        
        try {
            // 1. 获取奖励池关联的所有奖励配置
            List<RandomRewardPool> rewardPools = randomRewardPoolBuilder.findByPoolId(poolId, saasId);
            
            if (CollectionUtils.isEmpty(rewardPools)) {
                log.warn("随机奖励池为空: poolId={}, saasId={}", poolId, saasId);
                return pickedRewards;
            }
            
            log.info("随机奖励池物品数量: {}", rewardPools.size());
            
            // 2. 根据奖励池的随机算法和权重配置，动态组合生成最终的奖励内容
            List<RandomRewardPool> selectedPools = selectByWeight(rewardPools, pickCount);
            
            // 3. 生成最终奖励
            for (RandomRewardPool pool : selectedPools) {
                IssuedReward reward = createRewardFromPool(pool);
                if (reward != null) {
                    pickedRewards.add(reward);
                    log.info("抽取到奖励: {}", reward);
                }
            }
            
            log.info("随机池抽取完成: poolId={}, 实际获得奖励数量={}", poolId, pickedRewards.size());
            
        } catch (Exception e) {
            log.error("从随机池抽取奖励异常: poolId={}, pickCount={}", poolId, pickCount, e);
        }
        
        return pickedRewards;
    }
    
    @Override
    public IssuedReward processFixedItem(GiftPackConfig giftPackConfig) {
        return createIssuedReward(giftPackConfig);
    }

    /**
     * 创建发放奖励对象（固定物品）
     * 优化：使用物品主数据表查询物品详细信息
     */
    private IssuedReward createIssuedReward(GiftPackConfig config) {
        try {
            // 1. 从物品主数据表查询物品详细信息
            ItemMasterConfig itemConfig = itemMasterConfigBuilder.findByItemId(
                    config.getSaasId(), config.getItemId());
            
            if (itemConfig == null) {
                log.warn("未找到物品主数据: itemId={}, saasId={}", config.getItemId(), config.getSaasId());
                return null;
            }
            
            if (!itemConfig.getIsActive()) {
                log.warn("物品已停用: itemId={}, saasId={}", config.getItemId(), config.getSaasId());
                return null;
            }

            // 2. 计算随机数量（在最小值和最大值之间）
            Integer quantity = giftPackConfigProcessor.calculateRandomQuantity(
                    config.getQuantityMin(), config.getQuantityMax());

            // 3. 创建奖励对象，使用物品主数据表的信息
            IssuedReward reward = new IssuedReward();
            reward.setItemId(itemConfig.getExternalItemId());
            reward.setItemType(itemConfig.getItemType());
            reward.setItemName(itemConfig.getItemName());
            reward.setQuantity(quantity);
            reward.setDescription(itemConfig.getDescription());
            reward.setIconUrl(itemConfig.getItemIcon());

            return reward;

        } catch (Exception e) {
            log.error("创建发放奖励对象异常: packId={}", config.getPackId(), e);
            return null;
        }
    }
    
    @Override
    public List<RandomRewardPool> selectByWeight(List<RandomRewardPool> rewardPools, int count) {
        if (CollectionUtils.isEmpty(rewardPools) || count <= 0) {
            return new ArrayList<>();
        }
        
        List<RandomRewardPool> selected = new ArrayList<>();
        List<RandomRewardPool> availablePools = new ArrayList<>(rewardPools);
        
        try {
            // 计算总权重
            int totalWeight = availablePools.stream()
                    .mapToInt(pool -> pool.getWeight() != null ? pool.getWeight() : 1)
                    .sum();
            
            log.debug("权重随机选择: 总权重={}, 选择数量={}", totalWeight, count);
            
            // 按权重随机选择
            for (int i = 0; i < count && !availablePools.isEmpty(); i++) {
                RandomRewardPool selectedPool = selectSingleByWeight(availablePools, totalWeight);
                if (selectedPool != null) {
                    selected.add(selectedPool);
                    
                    // 从可选池中移除已选择的物品（避免重复）
                    availablePools.remove(selectedPool);
                    totalWeight -= (selectedPool.getWeight() != null ? selectedPool.getWeight() : 1);
                    
                    log.debug("选中奖励: itemId={}, weight={}", selectedPool.getItemId(), selectedPool.getWeight());
                }
            }
            
        } catch (Exception e) {
            log.error("权重随机选择异常: poolSize={}, count={}", rewardPools.size(), count, e);
        }
        
        return selected;
    }
    
    /**
     * 按权重随机选择单个奖励
     */
    private RandomRewardPool selectSingleByWeight(List<RandomRewardPool> pools, int totalWeight) {
        if (totalWeight <= 0) {
            return pools.get(ThreadLocalRandom.current().nextInt(pools.size()));
        }
        
        int randomValue = ThreadLocalRandom.current().nextInt(totalWeight);
        int currentWeight = 0;
        
        for (RandomRewardPool pool : pools) {
            int weight = pool.getWeight() != null ? pool.getWeight() : 1;
            currentWeight += weight;
            
            if (randomValue < currentWeight) {
                return pool;
            }
        }
        
        // 降级方案：如果权重计算有问题，随机选择一个
        return pools.get(ThreadLocalRandom.current().nextInt(pools.size()));
    }
    
    /**
     * 从奖励池配置创建奖励对象
     * 优化：使用物品主数据表查询物品详细信息
     */
    private IssuedReward createRewardFromPool(RandomRewardPool pool) {
        try {
            // 1. 从物品主数据表查询物品详细信息
            ItemMasterConfig itemConfig = itemMasterConfigBuilder.findByItemId(pool.getSaasId(), pool.getItemId());
            
            if (itemConfig == null) {
                log.warn("未找到物品主数据: itemId={}, saasId={}", pool.getItemId(), pool.getSaasId());
                return null;
            }
            
            if (!itemConfig.getIsActive()) {
                log.warn("物品已停用: itemId={}, saasId={}", pool.getItemId(), pool.getSaasId());
                return null;
            }
            
            // 2. 计算随机数量
            Integer quantity = giftPackConfigProcessor.calculateRandomQuantity(pool.getQuantityMin(), pool.getQuantityMax());
            
            // 3. 使用物品主数据表的信息创建奖励对象
            return new IssuedReward(
                    itemConfig.getExternalItemId(),
                    itemConfig.getItemType(),
                    itemConfig.getItemName(),
                    quantity,
                    itemConfig.getDescription(),
                    itemConfig.getItemIcon()
            );
            
        } catch (Exception e) {
            log.error("创建奖励对象异常: poolId={}, itemId={}", pool.getPoolId(), pool.getItemId(), e);
            return null;
        }
    }

    /**
     * 处理道具奖励
     */
    private List<IssuedReward> handleItemReward(ProgressChestConfig chestConfig, Map<String, String> userPreferences, String userId, String saasId) {
        List<IssuedReward> rewards = new ArrayList<>();

        try {
            // 1. 解析偏好映射
            PreferenceMapping.RewardConfig rewardConfig = getRewardConfigFromPreferences(chestConfig, userPreferences);

            if (rewardConfig != null && rewardConfig.isItemReward()) {
                // 使用偏好映射中的道具配置
                IssuedReward reward = issueDirectItem(rewardConfig.getItemId(), rewardConfig.getQuantity(), userId, saasId);
                if (reward != null) {
                    rewards.add(reward);
                }
            } else {
                // 使用默认配置
                String defaultItemId = chestConfig.getPackIdOnUnlock();
                Integer defaultQuantity = chestConfig.getQuantity() != null ? chestConfig.getQuantity() : 1;

                IssuedReward reward = issueDirectItem(defaultItemId, defaultQuantity, userId, saasId);
                if (reward != null) {
                    rewards.add(reward);
                }
            }

        } catch (Exception e) {
            log.error("处理道具奖励异常: chestId={}, userId={}, saasId={}", chestConfig.getChestId(), userId, saasId, e);
        }

        return rewards;
    }

    /**
     * 处理礼包奖励
     */
    private List<IssuedReward> handleGiftPackReward(ProgressChestConfig chestConfig, Map<String, String> userPreferences, String saasId) {
        try {
            // 1. 解析偏好映射
            PreferenceMapping.RewardConfig rewardConfig = getRewardConfigFromPreferences(chestConfig, userPreferences);

            if (rewardConfig != null && rewardConfig.isGiftPackReward()) {
                // 使用偏好映射中的礼包配置
                return issueRewardsByPackId(rewardConfig.getPackId(), saasId);
            } else {
                // 使用默认配置
                return issueRewardsByPackId(chestConfig.getPackIdOnUnlock(), saasId);
            }

        } catch (Exception e) {
            log.error("处理礼包奖励异常: chestId={}, saasId={}", chestConfig.getChestId(), saasId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 从偏好映射中获取奖励配置
     */
    private PreferenceMapping.RewardConfig getRewardConfigFromPreferences(ProgressChestConfig chestConfig, Map<String, String> userPreferences) {
        try {
            if (!StringUtils.hasText(chestConfig.getExtraProperties()) || userPreferences == null || userPreferences.isEmpty()) {
                return null;
            }

            // 解析 extra_properties 中的偏好映射
            PreferenceMapping preferenceMapping = JSON.parseObject(chestConfig.getExtraProperties(), PreferenceMapping.class);
            if (preferenceMapping == null || preferenceMapping.getPreferenceMapping() == null) {
                return null;
            }

            // 遍历用户偏好，查找匹配的奖励配置
            for (Map.Entry<String, String> userPref : userPreferences.entrySet()) {
                String preferenceType = userPref.getKey();
                String preferenceValue = userPref.getValue();

                Map<String, PreferenceMapping.RewardConfig> typeMapping = preferenceMapping.getPreferenceMapping().get(preferenceType);
                if (typeMapping != null) {
                    PreferenceMapping.RewardConfig rewardConfig = typeMapping.get(preferenceValue);
                    if (rewardConfig != null) {
                        log.debug("找到匹配的偏好奖励配置: preferenceType={}, preferenceValue={}, rewardConfig={}",
                                preferenceType, preferenceValue, rewardConfig);
                        return rewardConfig;
                    }
                }
            }

        } catch (Exception e) {
            log.error("解析偏好映射异常: chestId={}, userPreferences={}", chestConfig.getChestId(), userPreferences, e);
        }

        return null;
    }

}
