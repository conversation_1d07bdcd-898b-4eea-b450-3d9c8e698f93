package com.kikitrade.activity.service.draw.strategy;

import com.kikitrade.activity.dal.tablestore.model.ItemMasterConfig;
import com.kikitrade.activity.service.draw.RewardIssueService;
import com.kikitrade.activity.service.draw.RewardIssueService.IssuedReward;
import com.kikitrade.activity.service.draw.RewardIssueStrategy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 礼包发放策略
 * 对应item_type=GIFT_PACK的发奖逻辑
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class GiftPackRewardIssueStrategy implements RewardIssueStrategy {

    @Resource
    private RewardIssueService rewardIssueService;

    @Override
    public boolean supports(String itemType) {
        return "GIFT_PACK".equals(itemType);
    }

    @Override
    public IssuedReward issueReward(ItemMasterConfig itemConfig, Integer quantity, String userId, String saasId) {
//        try {
//            log.info("开启礼包: userId={}, packId={}, quantity={}",
//                    userId, itemConfig.getItemId(), quantity);
//
//            // 调用奖励中台自身的"开启礼包"逻辑，传递 reward_item_id (即 pack_id)
//            // 注意：礼包的quantity通常为1，即开启多少个礼包
//            for (int i = 0; i < quantity; i++) {
//                rewardIssueService.issueRewardsByPackId(itemConfig.getItemId(), saasId);
//            }
//
//            // 创建发放结果（礼包本身作为奖励）
//            IssuedReward reward = new IssuedReward();
//            reward.setItemId(itemConfig.getItemId());
//            reward.setItemType(itemConfig.getItemType());
//            reward.setItemName(itemConfig.getItemName());
//            reward.setQuantity(quantity);
//            reward.setDescription(itemConfig.getDescription());
//            reward.setIconUrl(itemConfig.getItemIcon());
//
//            log.info("礼包开启成功: userId={}, packId={}", userId, itemConfig.getItemId());
//            return reward;
//
//        } catch (Exception e) {
//            log.error("礼包开启失败: userId={}, packId={}", userId, itemConfig.getItemId(), e);
//            return null;
//        }
        return null;
    }
}
