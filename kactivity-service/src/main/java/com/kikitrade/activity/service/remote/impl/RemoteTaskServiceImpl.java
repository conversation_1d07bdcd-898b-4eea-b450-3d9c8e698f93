package com.kikitrade.activity.service.remote.impl;

import com.kikitrade.activity.api.RemoteTaskService;
import com.kikitrade.activity.api.model.*;
import com.kikitrade.activity.api.model.request.TaskListRequest;
import com.kikitrade.activity.api.model.response.*;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.mq.ActivityEventMessage;
import com.kikitrade.activity.service.task.ActivityTaskService;
import com.kikitrade.activity.service.task.TaskConfigService;
import com.kikitrade.activity.service.task.action.ActivityEventAction;
import com.kikitrade.activity.service.task.domain.TaskCycleDomain;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

@DubboService
@Slf4j
public class RemoteTaskServiceImpl implements RemoteTaskService {

    @Resource
    private ActivityTaskService activityTaskService;
    @Resource
    private ActivityEventAction activityEventAction;
    @Resource
    private TaskConfigService taskConfigService;

    /**
     * 任务列表
     * @param request
     * @return
     */
    @Override
    public List<TaskListResponse> taskList(TaskListRequest request) {
        if(StringUtils.isBlank(request.getSaasId())){
            return new ArrayList<>();
        }
        return activityTaskService.taskList(request);
    }

    /**
     * 任务详情
     * @param taskId
     * @return
     */
    @Override
    public TaskDetailResponse getTask(String taskId, String customerId) {
        return activityTaskService.findByTaskId(taskId, customerId, ActivityConstant.VipLevelEnum.NORMAL, "app");
    }

    /**
     * 根据 code 查询任务详情
     *
     * @param taskCode
     * @param customerId
     * @return
     */
    @Override
    public TaskCodeDetailResponse getTaskByCode(String saasId, String taskCode, String customerId) {
        return activityTaskService.findByTaskCode(saasId, taskCode, customerId);
    }

    @Override
    public List<Award> getTaskRewardByCode(String saasId, String taskCode, ActivityConstant.VipLevelEnum vipLevelEnum){
        return activityTaskService.findByTaskCode(saasId, taskCode, vipLevelEnum);
    }

    @Override
    public List<Award> listTaskRewards(String saasId, String taskCode, ActivityConstant.VipLevelEnum vipLevelEnum){
        return activityTaskService.listTaskRewards(saasId, taskCode, vipLevelEnum);
    }

    /**
     * 任务进度，暂时只支持组合任务的奖励的完成状态
     * @param saasId
     * @param taskId
     * @param customerId
     * @param type
     * @return
     */
    public TaskProgressResponse getTaskProgress(String saasId, String taskId, String customerId, String type){
        return activityTaskService.findProgressByTaskId(saasId, taskId, customerId, type);
    }

    @Override
    public TaskCompletedResult getTaskStatus(String saasId, String taskId, String customerId) {
        TaskConfigDTO taskConfig = taskConfigService.findByTaskIdAndWhiteFlag(taskId, customerId);
        if (Objects.isNull(taskConfig)) {
            log.warn("getTaskStatus taskConfig is null");
            return null;
        }
        String cycle = TaskCycleDomain.getCurrencyCycle(taskConfig, null);
        return activityTaskService.getTaskResult(customerId, cycle, taskId);
    }

    /**
     * 获取任务状态
     *
     * @param saasId
     * @param taskCode
     * @param customerId
     * @return
     */
    @Override
    public TaskCompletedResult getTaskStatusByCode(String saasId, String taskCode, String customerId) {
        List<TaskConfigDTO> taskConfigs = taskConfigService.findByTaskCodeAndWhiteFlag(saasId, taskCode, customerId);
        if (Objects.isNull(taskConfigs)) {
            log.warn("getTaskStatus taskConfig is null");
            return null;
        }
        TaskConfigDTO taskConfig = taskConfigs.get(0);
        String cycle = TaskCycleDomain.getCurrencyCycle(taskConfig, null);
        return activityTaskService.getTaskResult(customerId, cycle, taskConfig.getTaskId());
    }

    /**
     * 做任务
     *
     * @param activityEventMessageDTO
     * @return
     */
    @Override
    public ActivityResponse<List<Award>> task(ActivityEventMessageDTO activityEventMessageDTO) {
        try{
            log.info("user_track [remoteTaskService-task] request activityEventMessageDTO : {}", activityEventMessageDTO);
            ActivityEventMessage activityEventMessage = new ActivityEventMessage();
            BeanUtils.copyProperties(activityEventMessageDTO, activityEventMessage);
            ActivityResponse<List<Award>> action = activityEventAction.action(activityEventMessage);
            log.info("user_track [remoteTaskService-task] end [{}]", action);
            return action;
        }catch (Exception ex){
            log.error("remoteTaskService task error:{}", activityEventMessageDTO, ex);
            return null;
        }
    }

    @Override
    public TaskCompletedResult getTaskStatusByTargetId(String saasId, String taskId, String targetId) {
        TaskConfigDTO taskConfigDTO = taskConfigService.findByTaskId(taskId);
        return activityTaskService.getTaskResultBySocial(null, targetId, TaskCycleDomain.getCurrencyCycle(taskConfigDTO, null), taskConfigDTO.getTaskId());
    }
}
