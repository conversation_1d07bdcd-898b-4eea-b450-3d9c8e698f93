package com.kikitrade.activity.service.common;

import com.kikitrade.activity.service.draw.RewardIssueStrategy;
import com.kikitrade.activity.service.draw.RewardIssueStrategyFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class IssueConfiguration {

    @Bean
    public RewardIssueStrategyFactory rewardIssueStrategyFactory(List<RewardIssueStrategy> strategies) {
        return new RewardIssueStrategyFactory(strategies);
    }
}
