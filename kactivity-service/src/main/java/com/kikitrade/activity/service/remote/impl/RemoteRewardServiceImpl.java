package com.kikitrade.activity.service.remote.impl;

import com.kikitrade.activity.api.RemoteRewardService;
import com.kikitrade.activity.api.model.ActivityRewardDTO;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.api.model.request.reward.ActivityRewardRequest;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.service.reward.RewardService;
import com.kikitrade.activity.model.domain.RewardRequest;
import com.kikitrade.activity.service.task.TaskConfigService;
import com.kikitrade.activity.service.task.domain.TaskCycleDomain;
import com.kikitrade.framework.common.util.BeanUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/27 17:47
 */
@DubboService
@Slf4j
public class RemoteRewardServiceImpl implements RemoteRewardService {

    @Resource
    private TaskConfigService taskConfigService;
    @Resource
    private RewardService rewardService;

    @Override
    public ActivityResponse receiveReward(String customerId, String taskId) throws Exception {
        return rewardService.receiveReward(customerId, taskId);
    }

    @Override
    public ActivityRewardDTO findReward(ActivityRewardRequest activityRewardRequest) {

        List<TaskConfigDTO> code = taskConfigService.findByTaskCode(activityRewardRequest.getSaasId(), activityRewardRequest.getBusinessType(), null);
        if(code == null || code.isEmpty()) {
            return null;
        }
        String currencyCycle = TaskCycleDomain.getCurrencyCycle(code.get(0), System.currentTimeMillis());
        RewardRequest rewardRequest = RewardRequest.builder()
                .customerId(activityRewardRequest.getCustomerId())
                .businessType(activityRewardRequest.getBusinessType())
                .batchId(currencyCycle)
                .build();
        ActivityCustomReward reward = rewardService.getReward(rewardRequest);
        if(reward == null) {
            return null;
        }
        return BeanUtil.copyProperties(activityRewardRequest, ActivityRewardDTO::new);
    }
}
