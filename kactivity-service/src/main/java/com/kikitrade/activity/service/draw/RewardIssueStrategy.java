package com.kikitrade.activity.service.draw;

import com.kikitrade.activity.dal.tablestore.model.ItemMasterConfig;
import com.kikitrade.activity.service.draw.RewardIssueService.IssuedReward;

/**
 * 奖励发放策略接口
 * 根据技术规格书4.1节，实现基于item_type的发奖策略
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface RewardIssueStrategy {

    /**
     * 判断是否支持此类型的物品发放
     * 
     * @param itemType 物品类型
     * @return 是否支持
     */
    boolean supports(String itemType);

    /**
     * 执行奖励发放
     * 
     * @param itemConfig 物品主数据配置
     * @param quantity 发放数量
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @return 发放结果
     */
    IssuedReward issueReward(ItemMasterConfig itemConfig, Integer quantity, String userId, String saasId);
}
