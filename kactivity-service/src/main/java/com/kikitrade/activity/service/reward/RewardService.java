package com.kikitrade.activity.service.reward;

import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.model.domain.RewardRequest;

/**
 * Reward Service
 *
 * <AUTHOR>
 * @create 2021/10/25 7:52 下午
 * @modify
 */
public interface RewardService {

    /**
     * 统一发奖入口
     *
     * @param request
     * @return
     * @throws Exception
     */
    ActivityResponse reward(RewardRequest request) throws Exception;

    /**
     * 查询我带领取的奖励
     * @return
     */
    ActivityCustomReward getReward(RewardRequest rewardRequest);

    /**
     * 领取钱包
     * @param customerId
     * @param taskId
     * @return
     */
    ActivityResponse receiveReward(String customerId, String taskId);
}
