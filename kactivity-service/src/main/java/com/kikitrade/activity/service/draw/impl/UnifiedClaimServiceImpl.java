package com.kikitrade.activity.service.draw.impl;

import com.kikitrade.activity.dal.tablestore.builder.UserClaimEntitlementBuilder;
import com.kikitrade.activity.dal.tablestore.builder.UserPreferenceBuilder;
import com.kikitrade.activity.dal.tablestore.model.UserClaimEntitlement;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.draw.RewardIssueService;
import com.kikitrade.activity.service.draw.UnifiedClaimService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;


import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 统一领奖服务实现
 * 按照技术规格书要求实现统一的领奖能力
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class UnifiedClaimServiceImpl implements UnifiedClaimService {
    
    @Resource
    private UserClaimEntitlementBuilder userClaimEntitlementBuilder;

    @Resource
    private UserPreferenceBuilder userPreferenceBuilder;

    @Resource
    private com.kikitrade.activity.dal.tablestore.builder.ProgressChestConfigBuilder progressChestConfigBuilder;

    @Resource
    private RewardIssueService rewardIssueService;

    @Override
    public EntitlementCreateResult createEntitlement(CreateEntitlementRequest request) {
        log.info("创建领奖凭证: userId={}, rewardType={}, rewardSourceId={}", 
                request.getUserId(), request.getRewardType(), request.getRewardSourceId());
        
        try {
            // 1. 幂等性检查
            if (StringUtils.hasText(request.getSourceTransactionId())) {
                UserClaimEntitlement existing = userClaimEntitlementBuilder.findBySourceTransactionId(
                        request.getSourceTransactionId(), request.getSaasId());
                if (existing != null) {
                    log.info("凭证已存在，返回现有凭证: claimId={}", existing.getClaimId());
                    return null;
                }
            }
            
            // 2. 创建凭证
            UserClaimEntitlement entitlement = createEntitlementEntity(request);
            
            // 3. 保存凭证
            userClaimEntitlementBuilder.save(entitlement);
            
            log.info("领奖凭证创建成功: claimId={}", entitlement.getClaimId());
            
            return EntitlementCreateResult.builder().success(true).claimId(entitlement.getClaimId()).build();
            
        } catch (Exception e) {
            log.error("创建领奖凭证异常: userId={}, rewardType={}", 
                    request.getUserId(), request.getRewardType(), e);
            return null;
        }
    }
    
    @Override
    public UnifiedClaimResult claimReward(String userId, String claimId) {
        log.info("统一领奖: userId={}, claimId={}", userId, claimId);
        
        try {
            // 1. 验证凭证有效性
            UserClaimEntitlement entitlement = userClaimEntitlementBuilder.findByUserIdAndClaimId(userId, claimId);
            
            if (entitlement == null) {
                return new UnifiedClaimResult(false, "ENTITLEMENT_NOT_FOUND", "凭证不存在", null);
            }
            
            if (!ActivityConstant.EntitlementStatusEnum.UNCLAIMED.name().equals(entitlement.getStatus())) {
                return new UnifiedClaimResult(false, "ENTITLEMENT_ALREADY_CLAIMED", "凭证不可领取", null);
            }
            
            // 2. 检查是否过期
            if (isEntitlementExpired(entitlement)) {
                // 更新状态为过期
                entitlement.setStatus(ActivityConstant.EntitlementStatusEnum.EXPIRED.name());
                entitlement.setUpdateTime(System.currentTimeMillis());
                userClaimEntitlementBuilder.save(entitlement);
                
                return new UnifiedClaimResult(false, "ENTITLEMENT_EXPIRED", "凭证已过期", null);
            }
            
            // 3. 根据奖励类型执行相应的领奖逻辑
            List<ClaimedReward> rewards = executeClaimLogic(entitlement);
            
            if (rewards.isEmpty()) {
                return new UnifiedClaimResult(false, "CLAIM_FAILED", "奖励发放失败", null);
            }
            
            // 4. 更新凭证状态
            entitlement.setStatus(ActivityConstant.EntitlementStatusEnum.CLAIMED.name());
            entitlement.setClaimTime(System.currentTimeMillis());
            entitlement.setUpdateTime(System.currentTimeMillis());
            userClaimEntitlementBuilder.save(entitlement);
            
            log.info("统一领奖成功: userId={}, claimId={}, rewards={}", userId, claimId, rewards.size());
            
            return new UnifiedClaimResult(true, "", "领取成功", rewards);
            
        } catch (Exception e) {
            log.error("统一领奖异常: userId={}, claimId={}", userId, claimId, e);
            return new UnifiedClaimResult(false, "SYSTEM_ERROR", "系统异常", null);
        }
    }
    
    @Override
    public List<ClaimableReward> getClaimableRewards(String userId) {
        log.debug("获取可领取奖励: userId={}", userId);
        
        try {
            List<UserClaimEntitlement> entitlements = userClaimEntitlementBuilder.findUnclaimedByUserId(userId);
            
            return entitlements.stream()
                    .filter(e -> !isEntitlementExpired(e)) // 过滤过期凭证
                    .map(this::convertToClaimableReward)
                    .collect(Collectors.toList());
            
        } catch (Exception e) {
            log.error("获取可领取奖励异常: userId={}", userId, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<EntitlementCreateResult> batchCreateEntitlements(List<CreateEntitlementRequest> requests) {
        log.info("批量创建领奖凭证: count={}", requests.size());
        
        List<EntitlementCreateResult> results = new ArrayList<>();
        
        for (CreateEntitlementRequest request : requests) {
            try {
                EntitlementCreateResult result = createEntitlement(request);
                results.add(result);
            } catch (Exception e) {
                log.error("批量创建凭证失败: userId={}, rewardType={}", 
                        request.getUserId(), request.getRewardType(), e);
                results.add(new EntitlementCreateResult(false, null,"SYSTEM_ERROR", "系统异常"));
            }
        }
        
        log.info("批量创建凭证完成: 总数={}, 成功={}", requests.size(), 
                results.stream().mapToInt(r -> r.isSuccess() ? 1 : 0).sum());
        
        return results;
    }
    
    @Override
    public int cleanupExpiredEntitlements(String saasId) {
        log.info("清理过期凭证: saasId={}", saasId);
        
        try {
            long currentTime = System.currentTimeMillis();
            List<UserClaimEntitlement> expiredEntitlements = userClaimEntitlementBuilder.findExpiredEntitlements(currentTime, saasId);
            
            int cleanupCount = 0;
            for (UserClaimEntitlement entitlement : expiredEntitlements) {
                entitlement.setStatus(ActivityConstant.EntitlementStatusEnum.EXPIRED.name());
                entitlement.setUpdateTime(currentTime);
                userClaimEntitlementBuilder.save(entitlement);
                cleanupCount++;
            }
            
            log.info("过期凭证清理完成: saasId={}, 清理数量={}", saasId, cleanupCount);
            return cleanupCount;
            
        } catch (Exception e) {
            log.error("清理过期凭证异常: saasId={}", saasId, e);
            return 0;
        }
    }
    
    /**
     * 创建凭证实体
     */
    private UserClaimEntitlement createEntitlementEntity(CreateEntitlementRequest request) {
        UserClaimEntitlement entitlement = new UserClaimEntitlement();
        
        entitlement.setClaimId(generateClaimId());
        entitlement.setUserId(request.getUserId());
        entitlement.setSaasId(request.getSaasId());
        entitlement.setSceneCode(request.getSceneCode());
        entitlement.setRewardType(request.getRewardType());
        entitlement.setRewardSourceId(request.getRewardSourceId());
        entitlement.setRewardName(request.getRewardName());
        entitlement.setRewardIcon(request.getRewardIcon());
        entitlement.setRewardDescription(request.getRewardDescription());
        entitlement.setStatus(ActivityConstant.EntitlementStatusEnum.UNCLAIMED.name());
        entitlement.setSourceChannel(request.getSourceChannel());
        entitlement.setSourceTransactionId(request.getSourceTransactionId());
        
        long currentTime = System.currentTimeMillis();
        entitlement.setCreateTime(currentTime);
        entitlement.setUpdateTime(currentTime);
        
        // 设置过期时间
        if (request.getExpireTime() != null) {
            entitlement.setExpireTime(request.getExpireTime());
        }
        
        return entitlement;
    }
    
    /**
     * 执行领奖逻辑
     */
    private List<ClaimedReward> executeClaimLogic(UserClaimEntitlement entitlement) {
        List<ClaimedReward> rewards = new ArrayList<>();
        
        try {
            switch (ActivityConstant.RewardTypeEnum.valueOf(entitlement.getRewardType())) {
                case PROGRESS_CHEST:
                    rewards = claimProgressChest(entitlement);
                    break;
                    
                case GRANTED_PACK:
                    rewards = claimGrantedPack(entitlement);
                    break;

                default:
                    log.warn("未知的奖励类型: {}", entitlement.getRewardType());
            }
            
        } catch (Exception e) {
            log.error("执行领奖逻辑异常: claimId={}, rewardType={}", 
                    entitlement.getClaimId(), entitlement.getRewardType(), e);
        }
        
        return rewards;
    }
    
    /**
     * 领取进度宝箱（支持用户偏好映射）
     */
    private List<ClaimedReward> claimProgressChest(UserClaimEntitlement entitlement) {
        try {
            // 获取用户偏好
            java.util.Map<String, String> userPreferences = userPreferenceBuilder.getUserPreferenceMap(
                entitlement.getUserId(),
                extractPrizePoolCodeFromEntitlement(entitlement)
            );

            // 使用新的奖励发放逻辑，支持偏好映射
            List<RewardIssueService.IssuedReward> issuedRewards = rewardIssueService.issueRewardsByChestIdWithPreferences(
                entitlement.getRewardSourceId(),
                entitlement.getUserId(),
                entitlement.getSaasId(),
                userPreferences
            );

            return issuedRewards.stream()
                .map(reward -> new ClaimedReward(reward.getItemId(), reward.getItemName(), reward.getQuantity()))
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("领取进度宝箱异常: claimId={}, userId={}", entitlement.getClaimId(), entitlement.getUserId(), e);

            // 降级处理：使用原有逻辑
            List<RewardIssueService.IssuedReward> issuedRewards = rewardIssueService.issueRewardsByChestId(
                entitlement.getRewardSourceId(), entitlement.getSaasId());

            return issuedRewards.stream()
                .map(reward -> new ClaimedReward(reward.getItemId(), reward.getItemName(), reward.getQuantity()))
                .collect(Collectors.toList());
        }
    }
    
    /**
     * 领取授予的礼包
     */
    private List<ClaimedReward> claimGrantedPack(UserClaimEntitlement entitlement) {
        // 使用奖励发放服务处理礼包
        List<RewardIssueService.IssuedReward> issuedRewards = rewardIssueService.issueRewardsByPackId(
                entitlement.getRewardSourceId(), entitlement.getSaasId());
        
        return issuedRewards.stream()
                .map(reward -> new ClaimedReward(reward.getItemId(), reward.getItemName(), reward.getQuantity()))
                .collect(Collectors.toList());
    }
    
    /**
     * 领取直接奖励
     */
    private List<ClaimedReward> claimDirectReward(UserClaimEntitlement entitlement) {
        // 对于任务奖励和排行榜奖励，直接按照配置发放
        // 这里可以根据具体需求实现
        List<ClaimedReward> rewards = new ArrayList<>();
        rewards.add(new ClaimedReward("default_reward", "默认奖励", 1));
        return rewards;
    }
    
    /**
     * 检查凭证是否过期
     */
    private boolean isEntitlementExpired(UserClaimEntitlement entitlement) {
        return entitlement.getExpireTime() != null && 
               System.currentTimeMillis() > entitlement.getExpireTime();
    }
    
    /**
     * 转换为可领取奖励信息
     */
    private ClaimableReward convertToClaimableReward(UserClaimEntitlement entitlement) {
        ClaimableReward reward = new ClaimableReward();
        reward.setClaimId(entitlement.getClaimId());
        reward.setSceneCode(entitlement.getSceneCode());
        reward.setRewardType(entitlement.getRewardType());
        reward.setRewardName(entitlement.getRewardName());
        reward.setRewardIcon(entitlement.getRewardIcon());
        reward.setRewardDescription(entitlement.getRewardDescription());
        reward.setCreateTime(entitlement.getCreateTime());
        reward.setExpireTime(entitlement.getExpireTime());
        return reward;
    }
    
    /**
     * 生成ID
     */
    private String generateId() {
        return "UCE_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
    
    /**
     * 生成凭证ID
     */
    private String generateClaimId() {
        return UUID.randomUUID().toString();
    }

    /**
     * 从凭证中提取奖池编码
     * 通过查询宝箱配置来获取对应的奖池编码
     */
    private String extractPrizePoolCodeFromEntitlement(UserClaimEntitlement entitlement) {
        try {
            // 通过宝箱ID查询宝箱配置，获取奖池编码
            com.kikitrade.activity.dal.tablestore.model.ProgressChestConfig chestConfig =
                progressChestConfigBuilder.findByChestId(entitlement.getRewardSourceId(), entitlement.getSaasId());

            if (chestConfig != null) {
                String prizePoolCode = chestConfig.getPrizePoolCode();
                log.debug("从宝箱配置中获取奖池编码: chestId={}, prizePoolCode={}",
                        entitlement.getRewardSourceId(), prizePoolCode);
                return prizePoolCode != null ? prizePoolCode : "";
            }

        } catch (Exception e) {
            log.error("查询宝箱配置异常: chestId={}, saasId={}",
                    entitlement.getRewardSourceId(), entitlement.getSaasId(), e);
        }

        // 如果无法获取，返回空字符串，这样 getUserPreferenceMap 会返回空的偏好映射
        log.warn("无法从宝箱配置中获取奖池编码: claimId={}, chestId={}",
                entitlement.getClaimId(), entitlement.getRewardSourceId());
        return "";
    }
}
