package com.kikitrade.activity.service.draw.strategy;

import com.kikitrade.activity.dal.tablestore.model.ItemMasterConfig;
import com.kikitrade.activity.service.draw.RewardIssueService.IssuedReward;
import com.kikitrade.activity.service.draw.RewardIssueStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 游戏内货币发放策略
 * 对应item_type=CURRENCY的发奖逻辑
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class CurrencyRewardIssueStrategy implements RewardIssueStrategy {

    @Override
    public boolean supports(String itemType) {
        return "CURRENCY".equals(itemType);
    }

    @Override
    public IssuedReward issueReward(ItemMasterConfig itemConfig, Integer quantity, String userId, String saasId) {
        try {
            log.info("发放游戏内货币: userId={}, itemId={}, quantity={}", 
                    userId, itemConfig.getExternalItemId(), quantity);
            
            // TODO: 调用资产中心的"增加货币"接口，传递 item_id (如 "GOLD") 和 quantity
            // assetService.addCurrency(userId, itemConfig.getItemId(), quantity);
            
            // 创建发放结果
            IssuedReward reward = new IssuedReward();
            reward.setItemId(itemConfig.getExternalItemId());
            reward.setItemType(itemConfig.getItemType());
            reward.setItemName(itemConfig.getItemName());
            reward.setQuantity(quantity);
            reward.setDescription(itemConfig.getDescription());
            reward.setIconUrl(itemConfig.getItemIcon());
            
            log.info("游戏内货币发放成功: userId={}, itemId={}", userId, itemConfig.getExternalItemId());
            return reward;
            
        } catch (Exception e) {
            log.error("游戏内货币发放失败: userId={}, itemId={}", userId, itemConfig.getExternalItemId(), e);
            return null;
        }
    }
}
