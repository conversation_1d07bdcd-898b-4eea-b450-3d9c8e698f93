package com.kikitrade.activity.service.draw.preference.impl;

import com.kikitrade.activity.dal.tablestore.builder.UserPreferenceBuilder;
import com.kikitrade.activity.dal.tablestore.model.UserPreference;
import com.kikitrade.activity.service.draw.preference.UserPreferenceService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import java.util.Collections;
import java.util.Map;

/**
 * 用户偏好管理服务实现
 * 基于UserLotteryProfile存储用户偏好数据，使用JSON格式存储多种偏好类型
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class UserPreferenceServiceImpl implements UserPreferenceService {
    
    @Resource
    private UserPreferenceBuilder userPreferenceBuilder;

    @Override
    public boolean setUserPreference(String userId, String prizePoolCode, String preferenceType, String preferenceValue) {
        log.info("设置用户偏好: userId={}, prizePoolCode={}, type={}, value={}",
                userId, prizePoolCode, preferenceType, preferenceValue);
        
        try {
            //查找原值
            UserPreference userPreference = userPreferenceBuilder.findByUserIdAndPrizePoolCodeAndType(userId, prizePoolCode, preferenceType);
            if (userPreference != null) {
                userPreference.setPreferenceValue(preferenceValue);
                userPreference.setUpdateTime(System.currentTimeMillis());
                userPreferenceBuilder.update(userPreference);
                return true;
            }
            userPreference = new UserPreference();
            userPreference.setUserId(userId);
            userPreference.setPrizePoolCode(prizePoolCode);
            userPreference.setPreferenceType(preferenceType);
            userPreference.setPreferenceValue(preferenceValue);
            userPreference.setSource("USER_MANUAL");
            userPreference.setIsEnabled(true);
            userPreferenceBuilder.insert(userPreference);
            return true;
        } catch (Exception e) {
            log.error("设置用户偏好异常: userId={}, type={}, value={}", userId, preferenceType, preferenceValue, e);
            return false;
        }
    }
    
    @Override
    public String getUserPreference(String userId, String prizePoolCode, String preferenceType) {
        log.debug("获取用户偏好: userId={}, prizePoolCode={}, type={}", userId, prizePoolCode, preferenceType);
        
        try {
            UserPreference userPreference =
                userPreferenceBuilder.findByUserIdAndPrizePoolCodeAndType(userId, prizePoolCode, preferenceType);

            log.info("获取用户偏好结果: userId:{}, preferenceType:{}, preferenceValue:{}", userId, preferenceType, userPreference.getPreferenceValue());
            return userPreference.getPreferenceValue();
            
        } catch (Exception e) {
            log.error("获取用户偏好异常: userId={}, type={}", userId, preferenceType, e);
            return null;
        }
    }
    
    @Override
    public Map<String, String> getAllUserPreferences(String userId) {
        log.debug("获取用户所有偏好: userId={}", userId);
        
        try {

            return null;
        } catch (Exception e) {
            log.error("获取用户所有偏好异常: userId={}", userId, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<String, String> getAllUserPreferences(String userId, String prizePoolCode) {
        log.info("获取用户针对特定奖池的所有偏好: userId={}, prizePoolCode={}", userId, prizePoolCode);

        try {
            return userPreferenceBuilder.getUserPreferenceMap(userId, prizePoolCode);

        } catch (Exception e) {
            log.error("获取用户偏好异常: userId={}, prizePoolCode={}", userId, prizePoolCode, e);
            return Collections.emptyMap();
        }
    }
    
    @Override
    public boolean removeUserPreference(String userId, String saasId, String preferenceType) {
        log.info("删除用户偏好: userId={}, saasId={}, type={}", userId, saasId, preferenceType);
        
        // 设置为null即为删除
        return setUserPreference(userId, saasId, preferenceType, null);
    }
    
    @Override
    public boolean clearAllUserPreferences(String userId) {
        log.info("清空用户所有偏好: userId={}", userId);
        
        try {
           return true;
            
        } catch (Exception e) {
            log.error("清空用户偏好异常: userId={}", userId, e);
            return false;
        }
    }
}
