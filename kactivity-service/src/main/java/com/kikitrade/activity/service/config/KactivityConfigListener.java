package com.kikitrade.activity.service.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.listener.AbstractSharedListener;
import com.alibaba.nacos.api.exception.NacosException;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.dal.tablestore.builder.GoodsBuilder;
import com.kikitrade.activity.dal.tablestore.builder.PrizePoolBuilder;
import com.kikitrade.activity.dal.tablestore.builder.PrizeConfigBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ItemMasterConfigBuilder;
import com.kikitrade.activity.dal.tablestore.builder.GiftPackConfigBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ProgressChestConfigBuilder;
import com.kikitrade.activity.dal.tablestore.builder.RandomRewardPoolBuilder;
import com.kikitrade.activity.dal.tablestore.model.Goods;
import com.kikitrade.activity.dal.tablestore.model.PrizePool;
import com.kikitrade.activity.dal.tablestore.model.PrizeConfig;
import com.kikitrade.activity.dal.tablestore.model.ItemMasterConfig;
import com.kikitrade.activity.dal.tablestore.model.GiftPackConfig;
import com.kikitrade.activity.dal.tablestore.model.ProgressChestConfig;
import com.kikitrade.activity.dal.tablestore.model.RandomRewardPool;
import com.kikitrade.activity.service.task.TaskConfigService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class KactivityConfigListener{

    private static final String MATERIAL_TEMPLATE_DATA_ID = "kactivity-material-template.json";
    private static final String ACTIVITY_TASK = "kactivity-task.json";
    private static final String GOODS = "goods.json";
    private static final String LOTTERY = "lottery.json";

    @Resource
    private PropertiesConfigService propertiesConfigService;
    @Resource
    private TaskConfigService taskConfigService;
    @Resource
    private GoodsBuilder goodsBuilder;
    @Resource
    private PrizePoolBuilder prizePoolBuilder;
    @Resource
    private PrizeConfigBuilder prizeConfigBuilder;
    @Resource
    private ItemMasterConfigBuilder itemMasterConfigBuilder;
    @Resource
    private GiftPackConfigBuilder giftPackConfigBuilder;
    @Resource
    private ProgressChestConfigBuilder progressChestConfigBuilder;
    @Resource
    private RandomRewardPoolBuilder randomRewardPoolBuilder;

    @PostConstruct
    public void init(){
        try {
            addKactivityTaskListener();
            addLotteryListener();
        } catch (NacosException e) {
            log.error("nacos addListener exception",e);
        }
    }

    /**
     * 监听物料模版变化
     * @throws NacosException
     */
    private void addKactivityMaterialTemplateListener() throws NacosException{
        String config = propertiesConfigService.getConfigService().getConfigAndSignListener(MATERIAL_TEMPLATE_DATA_ID, "kactivity", 1000, new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                log.info("mes->kactivity-material change, dataId:{}, groupId:{}, content:{}", s, s1, s2);
                KactivityMaterialTemplateConfig.load(s2);
            }
        });
        log.info("mes->kactivity-material init:{}", config);
        KactivityMaterialTemplateConfig.load(config);
    }

    /**
     * 任务配置
     * @throws NacosException
     */
    private void addKactivityTaskListener() throws NacosException{
        propertiesConfigService.getConfigService().addListener(ACTIVITY_TASK, "kactivity", new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                log.info("mes->kactivity-task change, dataId:{}, groupId:{}, content:{}", s, s1, s2);
                List<TaskConfigDTO> dtoList = new ArrayList<>();
                if(s2.trim().startsWith("[") && s2.trim().endsWith("]")){
                    dtoList = JSON.parseArray(s2, TaskConfigDTO.class);
                }else{
                    dtoList.add(JSON.parseObject(s2, TaskConfigDTO.class));
                }
                for(TaskConfigDTO configDTO : dtoList){
                    taskConfigService.upsert(configDTO, false);
                }
            }
        });
    }

    private void addGoodsListener() throws NacosException{
        propertiesConfigService.getConfigService().addListener(GOODS, "kactivity", new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                log.info("mes->goods change, dataId:{}, groupId:{}, content:{}", s, s1, s2);
                List<Goods> goodses = JSON.parseArray(s2, Goods.class);
                for(Goods goods : goodses){
                    Goods obj = goodsBuilder.findById(goods.getGoodsId());
                    if(obj != null){
                        goodsBuilder.update(goods);
                    }else{
                        goodsBuilder.insert(goods);
                    }
                }
            }
        });
    }

    /**
     * 抽奖配置监听器
     * @throws NacosException
     */
    private void addLotteryListener() throws NacosException{
        propertiesConfigService.getConfigService().addListener(LOTTERY, "kactivity", new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                log.info("mes->lottery change, dataId:{}, groupId:{}, content:{}", s, s1, s2);
                try {
                    parseLotteryConfig(s2);
                } catch (Exception e) {
                    log.error("解析lottery.json配置失败", e);
                }
            }
        });
    }

    /**
     * 解析lottery.json配置并插入到数据库
     * @param jsonContent JSON内容
     */
    private void parseLotteryConfig(String jsonContent) {
        try {
            // 解析JSON
            com.alibaba.fastjson.JSONObject lotteryConfig = JSON.parseObject(jsonContent);
            
            // 处理prize_pool配置
            if (lotteryConfig.containsKey("prize_pool")) {
                com.alibaba.fastjson.JSONArray prizePoolArray = lotteryConfig.getJSONArray("prize_pool");
                for (int i = 0; i < prizePoolArray.size(); i++) {
                    com.alibaba.fastjson.JSONObject prizePoolJson = prizePoolArray.getJSONObject(i);
                    PrizePool prizePool = JSON.parseObject(prizePoolJson.toJSONString(), PrizePool.class);
                    
                    // 设置code字段为prize_pool_code
                    prizePool.setCode(prizePoolJson.getString("prize_pool_code"));
                    
                    // 检查是否存在，存在则更新，不存在则插入
                    PrizePool existing = prizePoolBuilder.findByCodeAndSaasId(prizePool.getCode(), prizePool.getSaasId());
                    if (existing != null) {
                        prizePoolBuilder.update(prizePool);
                        log.info("更新奖池配置: {}", prizePool.getCode());
                    } else {
                        prizePoolBuilder.insert(prizePool);
                        log.info("插入奖池配置: {}", prizePool.getCode());
                    }
                }
            }

            // 处理prize_config配置
            if (lotteryConfig.containsKey("prize_config")) {
                com.alibaba.fastjson.JSONArray prizeConfigArray = lotteryConfig.getJSONArray("prize_config");
                for (int i = 0; i < prizeConfigArray.size(); i++) {
                    com.alibaba.fastjson.JSONObject prizeConfigJson = prizeConfigArray.getJSONObject(i);
                    PrizeConfig prizeConfig = JSON.parseObject(prizeConfigJson.toJSONString(), PrizeConfig.class);
                    
                    // 检查是否存在，存在则更新，不存在则插入
                    PrizeConfig existing = prizeConfigBuilder.findById(prizeConfig.getSaasId(), prizeConfig.getPrizePoolCode(), prizeConfig.getPrizeId());
                    if (existing != null) {
                        log.info("更新奖品配置: {}", prizeConfig.getPrizeId());
                        prizeConfigBuilder.update(prizeConfig);
                    } else {
                        log.info("插入奖品配置: {}", prizeConfig.getPrizeId());
                        prizeConfigBuilder.insert(prizeConfig);
                    }
                }
            }

            // 处理item_master_config配置
            if (lotteryConfig.containsKey("item_master_config")) {
                com.alibaba.fastjson.JSONArray itemMasterArray = lotteryConfig.getJSONArray("item_master_config");
                for (int i = 0; i < itemMasterArray.size(); i++) {
                    com.alibaba.fastjson.JSONObject itemMasterJson = itemMasterArray.getJSONObject(i);
                    ItemMasterConfig itemMaster = JSON.parseObject(itemMasterJson.toJSONString(), ItemMasterConfig.class);
                    
                    // 检查是否存在，存在则更新，不存在则插入
                    ItemMasterConfig existing = itemMasterConfigBuilder.findByItemId(itemMaster.getSaasId(), itemMaster.getItemId());
                    if (existing != null) {
                        itemMasterConfigBuilder.update(itemMaster);
                        log.info("更新物品主数据: {}", itemMaster.getItemId());
                    } else {
                        itemMasterConfigBuilder.insert(itemMaster);
                        log.info("插入物品主数据: {}", itemMaster.getItemId());
                    }
                }
            }

            // 处理gift_pack_config配置
            if (lotteryConfig.containsKey("gift_pack_config")) {
                com.alibaba.fastjson.JSONArray giftPackArray = lotteryConfig.getJSONArray("gift_pack_config");
                for (int i = 0; i < giftPackArray.size(); i++) {
                    com.alibaba.fastjson.JSONObject giftPackJson = giftPackArray.getJSONObject(i);
                    GiftPackConfig giftPack = JSON.parseObject(giftPackJson.toJSONString(), GiftPackConfig.class);
                    
                    // 检查是否存在，存在则更新，不存在则插入
                    GiftPackConfig existing = giftPackConfigBuilder.findBySaasIdAndPackIdAndRuleId(
                        giftPack.getSaasId(), giftPack.getPackId(), giftPack.getRuleId());
                    if (existing != null) {
                        giftPackConfigBuilder.update(giftPack);
                        log.info("更新礼包配置: {}-{}", giftPack.getPackId(), giftPack.getRuleId());
                    } else {
                        giftPackConfigBuilder.insert(giftPack);
                        log.info("插入礼包配置: {}-{}", giftPack.getPackId(), giftPack.getRuleId());
                    }
                }
            }

            // 处理progress_chest_config配置
            if (lotteryConfig.containsKey("progress_chest_config")) {
                com.alibaba.fastjson.JSONArray progressChestArray = lotteryConfig.getJSONArray("progress_chest_config");
                for (int i = 0; i < progressChestArray.size(); i++) {
                    com.alibaba.fastjson.JSONObject progressChestJson = progressChestArray.getJSONObject(i);
                    
                    ProgressChestConfig progressChest = new ProgressChestConfig();
                    progressChest.setSaasId(progressChestJson.getString("saas_id"));
                    progressChest.setPrizePoolCode(progressChestJson.getString("prizePoolCode"));
                    progressChest.setChestId(progressChestJson.getString("chest_id"));
                    progressChest.setUnlockProgress(progressChestJson.getInteger("progress_required"));
                    progressChest.setCreateTime(progressChestJson.getLong("create_time"));
                    progressChest.setUpdateTime(progressChestJson.getLong("update_time"));
                    
                    // 检查是否存在，存在则更新，不存在则插入
                    ProgressChestConfig existing = progressChestConfigBuilder.findByChestId(
                        progressChest.getChestId(), progressChest.getSaasId());
                    if (existing != null) {
                        progressChestConfigBuilder.update(progressChest);
                        log.info("更新进度宝箱配置: {}", progressChest.getChestId());
                    } else {
                        progressChestConfigBuilder.insert(progressChest);
                        log.info("插入进度宝箱配置: {}", progressChest.getChestId());
                    }
                }
            }

            // 处理random_reward_pool配置
            if (lotteryConfig.containsKey("random_reward_pool")) {
                com.alibaba.fastjson.JSONArray randomRewardArray = lotteryConfig.getJSONArray("random_reward_pool");
                for (int i = 0; i < randomRewardArray.size(); i++) {
                    com.alibaba.fastjson.JSONObject randomRewardJson = randomRewardArray.getJSONObject(i);
                    RandomRewardPool randomReward = JSON.parseObject(randomRewardJson.toJSONString(), RandomRewardPool.class);
                    
                    // 检查是否存在，存在则更新，不存在则插入
                    RandomRewardPool existing = randomRewardPoolBuilder.findBySaasIdAndPoolIdAndItemId(
                        randomReward.getSaasId(), randomReward.getPoolId(), randomReward.getItemId());
                    if (existing != null) {
                        randomRewardPoolBuilder.update(randomReward);
                        log.info("更新随机奖励池配置: {}-{}", randomReward.getPoolId(), randomReward.getItemId());
                    } else {
                        randomRewardPoolBuilder.insert(randomReward);
                        log.info("插入随机奖励池配置: {}-{}", randomReward.getPoolId(), randomReward.getItemId());
                    }
                }
            }

            log.info("lottery.json配置解析完成");
        } catch (Exception e) {
            log.error("解析lottery.json配置时发生错误", e);
            throw e;
        }
    }
}
