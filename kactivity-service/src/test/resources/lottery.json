{"prize_pool": [{"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "exchange_rules": "[{\"exchangeType\":\"POINTS_TO_TICKET_WITH_ALLIANCE_TRANSFER\",\"assetType\":\"POINTS\",\"options\":[{\"optionId\":\"OPTION_1_TICKET\",\"cost\":1200,\"tickets\":1,\"description\":\"1200积分抽1次\"},{\"optionId\":\"OPTION_5_TICKETS\",\"cost\":6000,\"tickets\":5,\"description\":\"6000积分抽5次\"}]}]", "probability_strategy": "OVERALL", "chest_cycle_days": 7, "status": "ACTIVE", "create_time": 1757299239138, "update_time": 1757299239138}], "prize_config": [{"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "prize_id": "cb6b0c3b-bc3e-4fd6-ad9c-14ad8b5818b9", "reward_type": "ITEM", "reward_item_id": "item_wheel_lv5ExpBook_obt2", "reward_icon": "prop_icon_023", "reward_quantity": 1, "winning_probability": 80, "stock_quantity": -1, "is_active": true, "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "prize_id": "0a21e6c3-f037-4e76-a10b-2f0c2c55b973", "reward_type": "ITEM", "reward_item_id": "item_bluePrintChest_obt2", "reward_icon": "prop_icon_029", "reward_quantity": 1, "winning_probability": 80, "stock_quantity": -1, "is_active": true, "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "prize_id": "0f75c4ac-ed66-4110-8d0d-23dd42b1dd03", "reward_type": "ITEM", "reward_item_id": "item_goldChest_obt2", "reward_icon": "prop_icon_002", "reward_quantity": 1, "winning_probability": 80, "stock_quantity": -1, "is_active": true, "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "prize_id": "eacb7f96-85f3-459c-a3dd-2c6374c1cd59", "reward_type": "ITEM", "reward_item_id": "item_50vit_obt2", "reward_icon": "prop_icon_052", "reward_quantity": 1, "winning_probability": 2, "stock_quantity": -1, "is_active": true, "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "prize_id": "e0e2b461-0452-4c43-9003-4107491a935b", "reward_type": "ITEM", "reward_item_id": "item_priestsHolyWater_obt2", "reward_icon": "prop_icon_031", "reward_quantity": 1, "winning_probability": 4, "stock_quantity": -1, "is_active": true, "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "prize_id": "1e882361-2773-4834-b418-3490eec2c6f2", "reward_type": "ITEM", "reward_item_id": "item_holyWater_obt2", "reward_icon": "prop_icon_030", "reward_quantity": 1, "winning_probability": 10, "stock_quantity": -1, "is_active": true, "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "prize_id": "e249ae31-9757-473e-86a2-ee051b1776ed", "reward_type": "ITEM", "reward_item_id": "item_3hTrainingSpeedUp_obt2", "reward_icon": "prop_icon_016", "reward_quantity": 1, "winning_probability": 3, "stock_quantity": -1, "is_active": true, "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "prize_id": "e1787400-20f3-4136-9d65-63702ade3bb8", "reward_type": "ITEM", "reward_item_id": "item_herculesSoulStone_obt2", "reward_icon": "prop_stone_hero001", "preference_type": "SELECTED_HERO", "preference_value": "71001", "reward_quantity": 1, "winning_probability": 1, "stock_quantity": -1, "is_active": true, "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "prize_id": "3c4426ef-f398-4c17-a07d-4c104d5be849", "reward_type": "ITEM", "reward_item_id": "item_batuKhanSoulStone_obt2", "reward_icon": "prop_stone_hero006", "preference_type": "SELECTED_HERO", "preference_value": "71005", "reward_quantity": 1, "winning_probability": 1, "stock_quantity": -1, "is_active": true, "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "prize_id": "42c8ba95-afb3-4f4e-8e84-45bcc6ce1b89", "reward_type": "ITEM", "reward_item_id": "item_medusaSoulStone_obt2", "reward_icon": "prop_stone_hero010", "preference_type": "SELECTED_HERO", "preference_value": "71007", "reward_quantity": 1, "winning_probability": 1, "stock_quantity": -1, "is_active": true, "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "prize_id": "0c58606a-3fe4-4c2f-8da0-fd97b95daa2a", "reward_type": "ITEM", "reward_item_id": "item_kingA<PERSON>urSoulStone_obt2", "reward_icon": "prop_stone_hero008", "preference_type": "SELECTED_HERO", "preference_value": "71008", "reward_quantity": 1, "winning_probability": 1, "stock_quantity": -1, "is_active": true, "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "prize_id": "87f64a43-9eee-4a58-822c-86523ad457dc", "reward_type": "ITEM", "reward_item_id": "item_fenrirSoulStone_obt2", "reward_icon": "prop_stone_hero013", "preference_type": "SELECTED_HERO", "preference_value": "71010", "reward_quantity": 1, "winning_probability": 1, "stock_quantity": -1, "is_active": true, "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "prize_id": "10b8ef62-9a41-41ef-90ee-a3a080815372", "reward_type": "ITEM", "reward_item_id": "item_cleopatraSoulStone_obt2", "reward_icon": "prop_stone_hero017", "preference_type": "SELECTED_HERO", "preference_value": "71011", "reward_quantity": 1, "winning_probability": 1, "stock_quantity": -1, "is_active": true, "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "prize_id": "61433c8e-41c5-4f29-9ae1-99cffc79108c", "reward_type": "ITEM", "reward_item_id": "item_uesugiKenshinSoulStone_obt2", "reward_icon": "prop_stone_hero011", "preference_type": "SELECTED_HERO", "preference_value": "71012", "reward_quantity": 1, "winning_probability": 1, "stock_quantity": -1, "is_active": true, "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "prize_id": "0eacf9aa-5a45-46f2-bd63-f5c1f6ff79f9", "reward_type": "ITEM", "reward_item_id": "item_queenOfShebaSoulStone_obt2", "reward_icon": "prop_stone_hero009", "preference_type": "SELECTED_HERO", "preference_value": "71013", "reward_quantity": 1, "winning_probability": 1, "stock_quantity": -1, "is_active": true, "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "prize_pool_code": "OBT2_HERO_WHEEL", "prize_id": "6c13dac7-07a3-4f3d-ae04-0394c178f40e", "reward_type": "ITEM", "reward_item_id": "item_thorSoulStone_obt2", "reward_icon": "prop_stone_hero012", "preference_type": "SELECTED_HERO", "preference_value": "71014", "reward_quantity": 1, "winning_probability": 1, "stock_quantity": -1, "is_active": true, "create_time": 1757299239138, "update_time": 1757299239138}], "item_master_config": [{"saas_id": "mugen", "item_id": "item_wheel_lv5ExpBook_obt2", "external_item_id": "60025", "item_name": "Level 5 EXP Book", "item_type": "ITEM", "item_icon": "prop_icon_023", "description": "等级5知识之书", "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "item_id": "item_bluePrintChest_obt2", "external_item_id": "61001", "item_name": "Blueprint Chest", "item_type": "ITEM", "item_icon": "prop_icon_029", "description": "图纸宝箱", "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "item_id": "item_goldChest_obt2", "external_item_id": "61002", "item_name": "Gold Chest", "item_type": "ITEM", "item_icon": "prop_icon_002", "description": "金币宝箱", "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "item_id": "item_50vit_obt2", "external_item_id": "80000", "item_name": "50 Vit", "item_type": "ITEM", "item_icon": "prop_icon_052", "description": "体力药剂", "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "item_id": "item_priestsHolyWater_obt2", "external_item_id": "60028", "item_name": "Priest's Holy Water", "item_type": "ITEM", "item_icon": "prop_icon_031", "description": "祭祀圣水", "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "item_id": "item_holyWater_obt2", "external_item_id": "60029", "item_name": "Holy Water", "item_type": "ITEM", "item_icon": "prop_icon_030", "description": "天然圣水", "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "item_id": "item_3hTrainingSpeedUp_obt2", "external_item_id": "62107", "item_name": "3h Training Speedup", "item_type": "ITEM", "item_icon": "prop_icon_016", "description": "3小时训练加速", "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "item_id": "item_herculesSoulStone_obt2", "external_item_id": "71101", "item_name": "Hercules Soul Stone", "item_type": "ITEM", "item_icon": "prop_stone_hero001", "description": "海格力斯雕像（橙）", "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "item_id": "item_batuKhanSoulStone_obt2", "external_item_id": "71105", "item_name": "<PERSON><PERSON>", "item_type": "ITEM", "item_icon": "prop_stone_hero006", "description": "拔都雕像（橙）", "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "item_id": "item_medusaSoulStone_obt2", "external_item_id": "71107", "item_name": "Medusa Soul Stone", "item_type": "ITEM", "item_icon": "prop_stone_hero010", "description": "（橙）", "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "item_id": "item_kingA<PERSON>urSoulStone_obt2", "external_item_id": "71108", "item_name": "<PERSON>", "item_type": "ITEM", "item_icon": "prop_stone_hero008", "description": "（橙）", "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "item_id": "item_fenrirSoulStone_obt2", "external_item_id": "71110", "item_name": "<PERSON><PERSON><PERSON>", "item_type": "ITEM", "item_icon": "prop_stone_hero013", "description": "（橙）", "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "item_id": "item_cleopatraSoulStone_obt2", "external_item_id": "71111", "item_name": "Cleopatra Soul Stone", "item_type": "ITEM", "item_icon": "prop_stone_hero017", "description": "（橙）", "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "item_id": "item_uesugiKenshinSoulStone_obt2", "external_item_id": "71112", "item_name": "<PERSON><PERSON><PERSON><PERSON>", "item_type": "ITEM", "item_icon": "prop_stone_hero011", "description": "（橙）", "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "item_id": "item_queenOfShebaSoulStone_obt2", "external_item_id": "71113", "item_name": "Queen of Sheba Soul Stone", "item_type": "ITEM", "item_icon": "prop_stone_hero009", "description": "（橙）", "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "item_id": "item_thorSoulStone_obt2", "external_item_id": "71114", "item_name": "<PERSON>", "item_type": "ITEM", "item_icon": "prop_stone_hero012", "description": "（橙）", "create_time": 1757299239138, "update_time": 1757299239138}], "gift_pack_config": [{"saas_id": "mugen", "pack_id": "obt2_nft_holder", "rule_id": "a46bea94-db13-4985-a3a6-cff8c994c760", "rule_type": "FIXED_ITEM", "item_id": "item_ody_token_obt2", "quantity_min": 500, "quantity_max": 2000, "create_time": 1757299239138, "update_time": 1757299239138}, {"saas_id": "mugen", "pack_id": "obt2_nft_holder", "rule_id": "df883e60-282f-43b9-a372-ccbf58fbe808", "rule_type": "RANDOM_POOL_PICK", "random_pool_id": "pool_nft_hodler_obt2", "quantity_min": 500, "quantity_max": 2000, "create_time": 1757299239138, "update_time": 1757299239138}], "progress_chest_config": [{"saas_id": "mugen", "prizePoolCode": "OBT2_HERO_WHEEL", "chest_id": "obt2_hero_wheel_chest_1", "displayOrder": 1, "unlockProgress": 10, "packIdOnUnlock": "obt2_pack_heroWheelChest_1"}, {"saas_id": "mugen", "prizePoolCode": "OBT2_HERO_WHEEL", "chest_id": "obt2_hero_wheel_chest_002", "displayOrder": 2, "unlockProgress": 25, "packIdOnUnlock": "obt2_pack_heroWheelChest_2"}, {"saas_id": "mugen", "prizePoolCode": "OBT2_HERO_WHEEL", "chest_id": "obt2_hero_wheel_chest_002", "displayOrder": 3, "unlockProgress": 50, "packIdOnUnlock": "obt2_pack_heroWheelChest_3"}, {"saas_id": "mugen", "prizePoolCode": "OBT2_HERO_WHEEL", "chest_id": "obt2_hero_wheel_chest_002", "preferenceType": "SELECTED_HERO", "preferenceValue": "71005", "displayOrder": 2, "unlockProgress": 25, "packIdOnUnlock": "pack_hero_a_001"}], "random_reward_pool": [{"saas_id": "mugen", "pool_id": "pool_nft_hodler_obt2", "item_id": "62006", "quantity_min": 10, "quantity_max": 10, "weight": 100, "create_time": 1757299239138, "update_time": 1757299239138}]}