{"saas_id": "mugen", "progress_chest_config": [{"saas_id": "mugen", "prize_pool_code": "ADVANCED_POOL", "chest_id": "chest_004", "reward_type": "ITEM", "pack_id_on_unlock": "gold", "quantity": 100, "unlock_progress": 5000, "progress_type": "POINTS_EARNED", "is_active": true, "extra_properties": {"preference_mapping": {"SELECTED_HERO": {"HERO_A": {"item_id": "gold", "quantity": 100}, "HERO_B": {"item_id": "exp_potion", "quantity": 50}, "HERO_C": {"item_id": "gem", "quantity": 20}, "HERO_D": {"item_id": "gold", "quantity": 120}, "HERO_E": {"item_id": "exp_potion", "quantity": 60}, "HERO_F": {"item_id": "gem", "quantity": 25}, "HERO_G": {"item_id": "gold", "quantity": 150}, "HERO_H": {"item_id": "exp_potion", "quantity": 80}, "HERO_I": {"item_id": "gem", "quantity": 30}}}}, "scene_code": "ADVANCED_PROGRESS", "chest_name": "Advanced Item Chest", "display_order": 1, "description": "Item chest based on hero preference", "icon_url": "https://example.com/chest_icon4.png", "valid_start_time": 1696600000000, "valid_end_time": 1704067200000, "chest_value": 100, "max_claim_per_user": 1, "reset_cycle_days": 0, "priority": 1, "create_time": 1696600000000, "update_time": 1696600000000, "creator": "admin"}, {"saas_id": "mugen", "prize_pool_code": "ADVANCED_POOL", "chest_id": "chest_005", "reward_type": "GIFT_PACK", "pack_id_on_unlock": "pack_005", "quantity": null, "unlock_progress": 10000, "progress_type": "POINTS_EARNED", "is_active": true, "extra_properties": {"preference_mapping": {"SELECTED_HERO": {"HERO_A": {"pack_id": "pack_005_A"}, "HERO_B": {"pack_id": "pack_005_B"}, "HERO_C": {"pack_id": "pack_005_C"}, "HERO_D": {"pack_id": "pack_005_D"}, "HERO_E": {"pack_id": "pack_005_E"}, "HERO_F": {"pack_id": "pack_005_F"}, "HERO_G": {"pack_id": "pack_005_G"}, "HERO_H": {"pack_id": "pack_005_H"}, "HERO_I": {"pack_id": "pack_005_I"}}}}, "scene_code": "ADVANCED_PROGRESS", "chest_name": "Advanced Gift Pack Chest", "display_order": 2, "description": "Gift pack chest based on hero preference", "icon_url": "https://example.com/chest_icon5.png", "valid_start_time": 1696600000000, "valid_end_time": 1704067200000, "chest_value": 200, "max_claim_per_user": 1, "reset_cycle_days": 0, "priority": 2, "create_time": 1696600000000, "update_time": 1696600000000, "creator": "admin"}, {"saas_id": "mugen", "prize_pool_code": "ADVANCED_POOL", "chest_id": "chest_006", "reward_type": "ITEM", "pack_id_on_unlock": "diamond", "quantity": 10, "unlock_progress": 20000, "progress_type": "POINTS_EARNED", "is_active": true, "extra_properties": {"preference_mapping": {"SELECTED_HERO": {"HERO_A": {"item_id": "diamond", "quantity": 10}, "HERO_B": {"item_id": "rare_gem", "quantity": 5}, "HERO_C": {"item_id": "epic_crystal", "quantity": 3}, "HERO_D": {"item_id": "diamond", "quantity": 12}, "HERO_E": {"item_id": "rare_gem", "quantity": 6}, "HERO_F": {"item_id": "epic_crystal", "quantity": 4}, "HERO_G": {"item_id": "diamond", "quantity": 15}, "HERO_H": {"item_id": "rare_gem", "quantity": 8}, "HERO_I": {"item_id": "epic_crystal", "quantity": 5}}}}, "scene_code": "ADVANCED_PROGRESS", "chest_name": "Ultimate Reward Chest", "display_order": 3, "description": "Ultimate reward chest with premium items", "icon_url": "https://example.com/chest_icon6.png", "valid_start_time": 1696600000000, "valid_end_time": 1704067200000, "chest_value": 500, "max_claim_per_user": 1, "reset_cycle_days": 0, "priority": 3, "create_time": 1696600000000, "update_time": 1696600000000, "creator": "admin"}], "gift_pack_config": [{"saas_id": "mugen", "pack_id": "pack_005_A", "rule_id": "rule_001", "rule_type": "FIXED_ITEM", "item_id": "gold", "quantity_min": 200, "quantity_max": 200, "create_time": 1696600000000, "update_time": 1696600000000}, {"saas_id": "mugen", "pack_id": "pack_005_B", "rule_id": "rule_002", "rule_type": "FIXED_ITEM", "item_id": "exp_potion", "quantity_min": 100, "quantity_max": 100, "create_time": 1696600000000, "update_time": 1696600000000}, {"saas_id": "mugen", "pack_id": "pack_005_C", "rule_id": "rule_003", "rule_type": "FIXED_ITEM", "item_id": "gem", "quantity_min": 50, "quantity_max": 50, "create_time": 1696600000000, "update_time": 1696600000000}], "item_master_config": [{"saas_id": "mugen", "item_id": "gold", "external_item_id": "GOLD_001", "item_name": "Gold Coin", "item_type": "CURRENCY", "description": "Basic currency for trading", "icon_url": "https://example.com/gold_icon.png", "create_time": 1696600000000, "update_time": 1696600000000}, {"saas_id": "mugen", "item_id": "exp_potion", "external_item_id": "EXP_001", "item_name": "Experience Potion", "item_type": "CONSUMABLE", "description": "Increases hero experience", "icon_url": "https://example.com/exp_potion_icon.png", "create_time": 1696600000000, "update_time": 1696600000000}, {"saas_id": "mugen", "item_id": "gem", "external_item_id": "GEM_001", "item_name": "Precious Gem", "item_type": "MATERIAL", "description": "Rare crafting material", "icon_url": "https://example.com/gem_icon.png", "create_time": 1696600000000, "update_time": 1696600000000}, {"saas_id": "mugen", "item_id": "diamond", "external_item_id": "DIAMOND_001", "item_name": "Diamond", "item_type": "PREMIUM_CURRENCY", "description": "Premium currency for special purchases", "icon_url": "https://example.com/diamond_icon.png", "create_time": 1696600000000, "update_time": 1696600000000}, {"saas_id": "mugen", "item_id": "rare_gem", "external_item_id": "RARE_GEM_001", "item_name": "Rare Gem", "item_type": "MATERIAL", "description": "Very rare crafting material", "icon_url": "https://example.com/rare_gem_icon.png", "create_time": 1696600000000, "update_time": 1696600000000}, {"saas_id": "mugen", "item_id": "epic_crystal", "external_item_id": "EPIC_CRYSTAL_001", "item_name": "Epic Crystal", "item_type": "MATERIAL", "description": "Epic level crafting material", "icon_url": "https://example.com/epic_crystal_icon.png", "create_time": 1696600000000, "update_time": 1696600000000}]}