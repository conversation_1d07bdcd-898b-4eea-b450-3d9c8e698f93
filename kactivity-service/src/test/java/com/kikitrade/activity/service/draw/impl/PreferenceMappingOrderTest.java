package com.kikitrade.activity.service.draw.impl;

import com.alibaba.fastjson2.JSON;
import com.kikitrade.activity.model.domain.PreferenceMapping;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 偏好映射顺序遍历测试
 * 验证按照优化.md文档要求的顺序遍历逻辑
 */
public class PreferenceMappingOrderTest {

    @Test
    @DisplayName("测试偏好映射按顺序遍历逻辑")
    void testPreferenceMappingOrder() {
        // 创建多个偏好规则，测试优先级顺序
        PreferenceMapping preferenceMapping = createMultiplePreferenceRules();
        
        // 用户偏好：同时有英雄和阵营偏好
        Map<String, String> userPreferences = new HashMap<>();
        userPreferences.put("SELECTED_HERO", "HERO_A");
        userPreferences.put("SELECTED_FACTION", "LIGHT");
        
        // 模拟业务逻辑：按顺序遍历偏好映射规则
        PreferenceMapping.RewardConfig matchedConfig = findFirstMatchingReward(preferenceMapping, userPreferences);
        
        // 验证结果：应该匹配第一个规则（SELECTED_HERO），而不是第二个规则（SELECTED_FACTION）
        assertNotNull(matchedConfig);
        assertEquals("ITEM", matchedConfig.getRewardType());
        assertEquals("hero_a_sword", matchedConfig.getItemId());
        assertEquals(Integer.valueOf(1), matchedConfig.getQuantity());
    }

    @Test
    @DisplayName("测试当第一个规则不匹配时，使用第二个规则")
    void testFallbackToSecondRule() {
        // 创建多个偏好规则
        PreferenceMapping preferenceMapping = createMultiplePreferenceRules();
        
        // 用户偏好：只有阵营偏好，没有英雄偏好
        Map<String, String> userPreferences = new HashMap<>();
        userPreferences.put("SELECTED_FACTION", "LIGHT");
        
        // 模拟业务逻辑：按顺序遍历偏好映射规则
        PreferenceMapping.RewardConfig matchedConfig = findFirstMatchingReward(preferenceMapping, userPreferences);
        
        // 验证结果：应该匹配第二个规则（SELECTED_FACTION）
        assertNotNull(matchedConfig);
        assertEquals("GIFT_PACK", matchedConfig.getRewardType());
        assertEquals("light_faction_pack", matchedConfig.getPackId());
    }

    @Test
    @DisplayName("测试当所有规则都不匹配时，返回null")
    void testNoMatchingRules() {
        // 创建多个偏好规则
        PreferenceMapping preferenceMapping = createMultiplePreferenceRules();
        
        // 用户偏好：不匹配任何规则
        Map<String, String> userPreferences = new HashMap<>();
        userPreferences.put("SELECTED_WEAPON", "SWORD"); // 不存在的偏好类型
        
        // 模拟业务逻辑：按顺序遍历偏好映射规则
        PreferenceMapping.RewardConfig matchedConfig = findFirstMatchingReward(preferenceMapping, userPreferences);
        
        // 验证结果：应该返回null，表示需要使用默认配置
        assertNull(matchedConfig);
    }

    @Test
    @DisplayName("测试JSON序列化和反序列化")
    void testJsonSerialization() {
        // 创建偏好映射
        PreferenceMapping originalMapping = createMultiplePreferenceRules();
        
        // 序列化为JSON
        String json = JSON.toJSONString(originalMapping);
        assertNotNull(json);
        assertTrue(json.contains("preference_mapping"));
        assertTrue(json.contains("SELECTED_HERO"));
        assertTrue(json.contains("SELECTED_FACTION"));
        
        // 反序列化
        PreferenceMapping deserializedMapping = JSON.parseObject(json, PreferenceMapping.class);
        assertNotNull(deserializedMapping);
        assertNotNull(deserializedMapping.getPreferenceMapping());
        assertEquals(2, deserializedMapping.getPreferenceMapping().size());
        
        // 验证顺序保持不变
        assertEquals("SELECTED_HERO", deserializedMapping.getPreferenceMapping().get(0).getPreferenceType());
        assertEquals("SELECTED_FACTION", deserializedMapping.getPreferenceMapping().get(1).getPreferenceType());
    }

    @Test
    @DisplayName("测试复杂的偏好映射场景")
    void testComplexPreferenceMappingScenario() {
        // 创建复杂的偏好映射：包含多种偏好类型和奖励类型
        PreferenceMapping preferenceMapping = createComplexPreferenceRules();
        
        // 场景1：用户有所有类型的偏好，应该匹配第一个规则
        Map<String, String> userPrefs1 = new HashMap<>();
        userPrefs1.put("SELECTED_HERO", "HERO_A");
        userPrefs1.put("SELECTED_FACTION", "LIGHT");
        userPrefs1.put("VIP_LEVEL", "VIP_3");
        
        PreferenceMapping.RewardConfig config1 = findFirstMatchingReward(preferenceMapping, userPrefs1);
        assertNotNull(config1);
        assertEquals("SELECTED_HERO", findMatchingRuleType(preferenceMapping, userPrefs1));
        
        // 场景2：用户只有阵营和VIP偏好，应该匹配第二个规则
        Map<String, String> userPrefs2 = new HashMap<>();
        userPrefs2.put("SELECTED_FACTION", "DARK");
        userPrefs2.put("VIP_LEVEL", "VIP_5");
        
        PreferenceMapping.RewardConfig config2 = findFirstMatchingReward(preferenceMapping, userPrefs2);
        assertNotNull(config2);
        assertEquals("SELECTED_FACTION", findMatchingRuleType(preferenceMapping, userPrefs2));
        
        // 场景3：用户只有VIP偏好，应该匹配第三个规则
        Map<String, String> userPrefs3 = new HashMap<>();
        userPrefs3.put("VIP_LEVEL", "VIP_1");
        
        PreferenceMapping.RewardConfig config3 = findFirstMatchingReward(preferenceMapping, userPrefs3);
        assertNotNull(config3);
        assertEquals("VIP_LEVEL", findMatchingRuleType(preferenceMapping, userPrefs3));
    }

    /**
     * 模拟业务逻辑：按顺序遍历偏好映射规则，找到第一个匹配的规则
     * 这个方法复制了 RewardIssueServiceImpl.getRewardConfigFromPreferences 的核心逻辑
     */
    private PreferenceMapping.RewardConfig findFirstMatchingReward(PreferenceMapping preferenceMapping, Map<String, String> userPreferences) {
        if (preferenceMapping == null || preferenceMapping.getPreferenceMapping() == null || 
            preferenceMapping.getPreferenceMapping().isEmpty() || userPreferences == null || userPreferences.isEmpty()) {
            return null;
        }

        // 按顺序遍历偏好映射规则列表
        for (PreferenceMapping.PreferenceRule rule : preferenceMapping.getPreferenceMapping()) {
            String preferenceType = rule.getPreferenceType();
            
            // 检查用户是否有该类型的偏好设置
            if (userPreferences.containsKey(preferenceType)) {
                String userPreferenceValue = userPreferences.get(preferenceType);
                
                // 在该规则的映射中查找匹配的偏好值
                if (rule.getMapping() != null && rule.getMapping().containsKey(userPreferenceValue)) {
                    PreferenceMapping.RewardConfig rewardConfig = rule.getMapping().get(userPreferenceValue);
                    
                    // 找到第一个匹配的规则后立即返回
                    return rewardConfig;
                }
            }
        }

        return null;
    }

    /**
     * 查找匹配的规则类型（用于测试验证）
     */
    private String findMatchingRuleType(PreferenceMapping preferenceMapping, Map<String, String> userPreferences) {
        for (PreferenceMapping.PreferenceRule rule : preferenceMapping.getPreferenceMapping()) {
            String preferenceType = rule.getPreferenceType();
            
            if (userPreferences.containsKey(preferenceType)) {
                String userPreferenceValue = userPreferences.get(preferenceType);
                
                if (rule.getMapping() != null && rule.getMapping().containsKey(userPreferenceValue)) {
                    return preferenceType;
                }
            }
        }
        return null;
    }

    /**
     * 创建包含多个偏好规则的映射（按优先级顺序）
     */
    private PreferenceMapping createMultiplePreferenceRules() {
        List<PreferenceMapping.PreferenceRule> rules = new ArrayList<>();
        
        // 第一个规则：英雄偏好（优先级最高）
        Map<String, PreferenceMapping.RewardConfig> heroMapping = new HashMap<>();
        heroMapping.put("HERO_A", PreferenceMapping.RewardConfig.createItemReward("hero_a_sword", 1));
        heroMapping.put("HERO_B", PreferenceMapping.RewardConfig.createItemReward("hero_b_bow", 1));
        rules.add(new PreferenceMapping.PreferenceRule("SELECTED_HERO", heroMapping));
        
        // 第二个规则：阵营偏好（优先级次之）
        Map<String, PreferenceMapping.RewardConfig> factionMapping = new HashMap<>();
        factionMapping.put("LIGHT", PreferenceMapping.RewardConfig.createGiftPackReward("light_faction_pack"));
        factionMapping.put("DARK", PreferenceMapping.RewardConfig.createGiftPackReward("dark_faction_pack"));
        rules.add(new PreferenceMapping.PreferenceRule("SELECTED_FACTION", factionMapping));
        
        return new PreferenceMapping(rules);
    }

    /**
     * 创建复杂的偏好规则（包含三种偏好类型）
     */
    private PreferenceMapping createComplexPreferenceRules() {
        List<PreferenceMapping.PreferenceRule> rules = new ArrayList<>();
        
        // 第一个规则：英雄偏好
        Map<String, PreferenceMapping.RewardConfig> heroMapping = new HashMap<>();
        heroMapping.put("HERO_A", PreferenceMapping.RewardConfig.createItemReward("hero_a_weapon", 1));
        rules.add(new PreferenceMapping.PreferenceRule("SELECTED_HERO", heroMapping));
        
        // 第二个规则：阵营偏好
        Map<String, PreferenceMapping.RewardConfig> factionMapping = new HashMap<>();
        factionMapping.put("LIGHT", PreferenceMapping.RewardConfig.createGiftPackReward("light_pack"));
        factionMapping.put("DARK", PreferenceMapping.RewardConfig.createGiftPackReward("dark_pack"));
        rules.add(new PreferenceMapping.PreferenceRule("SELECTED_FACTION", factionMapping));
        
        // 第三个规则：VIP等级偏好
        Map<String, PreferenceMapping.RewardConfig> vipMapping = new HashMap<>();
        vipMapping.put("VIP_1", PreferenceMapping.RewardConfig.createItemReward("vip_coin", 100));
        vipMapping.put("VIP_3", PreferenceMapping.RewardConfig.createItemReward("vip_coin", 300));
        vipMapping.put("VIP_5", PreferenceMapping.RewardConfig.createItemReward("vip_coin", 500));
        rules.add(new PreferenceMapping.PreferenceRule("VIP_LEVEL", vipMapping));
        
        return new PreferenceMapping(rules);
    }
}
