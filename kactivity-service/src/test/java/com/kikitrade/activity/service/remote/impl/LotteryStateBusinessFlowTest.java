package com.kikitrade.activity.service.remote.impl;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.util.*;

/**
 * 抽奖状态业务流程测试
 * 验证用户偏好过滤和宝箱排序逻辑的核心算法
 */
public class LotteryStateBusinessFlowTest {

    /**
     * 模拟宝箱配置数据结构
     */
    static class MockChestConfig {
        String chestId;
        String preferenceType;
        String preferenceValue;
        Long displayOrder;
        Integer unlockProgress;

        MockChestConfig(String chestId, String preferenceType, String preferenceValue, 
                       Long displayOrder, Integer unlockProgress) {
            this.chestId = chestId;
            this.preferenceType = preferenceType;
            this.preferenceValue = preferenceValue;
            this.displayOrder = displayOrder;
            this.unlockProgress = unlockProgress;
        }
    }

    @Test
    @DisplayName("测试用户偏好过滤逻辑")
    void testUserPreferenceFiltering() {
        // 准备测试数据：用户选择了英雄A
        Map<String, String> userPreferences = new HashMap<>();
        userPreferences.put("SELECTED_HERO", "HERO_A_001");

        // 准备宝箱配置：包含通用宝箱和不同英雄的专属宝箱
        List<MockChestConfig> allChestConfigs = Arrays.asList(
            new MockChestConfig("chest_001", null, null, 1L, 10), // 通用宝箱
            new MockChestConfig("chest_002", "SELECTED_HERO", "HERO_A_001", 2L, 20), // 英雄A专属宝箱
            new MockChestConfig("chest_003", "SELECTED_HERO", "HERO_B_001", 3L, 30), // 英雄B专属宝箱（应被过滤）
            new MockChestConfig("chest_004", null, null, 4L, 40), // 另一个通用宝箱
            new MockChestConfig("chest_005", "SELECTED_HERO", "HERO_A_001", 5L, 50) // 另一个英雄A专属宝箱
        );

        // 执行过滤逻辑
        List<MockChestConfig> filteredConfigs = allChestConfigs.stream()
            .filter(config -> isChestValidForUser(config, userPreferences))
            .toList();

        // 验证过滤结果
        assertEquals(4, filteredConfigs.size(), "应该保留4个宝箱：2个通用 + 2个英雄A专属");
        
        // 验证具体的宝箱ID
        Set<String> expectedChestIds = Set.of("chest_001", "chest_002", "chest_004", "chest_005");
        Set<String> actualChestIds = filteredConfigs.stream()
            .map(config -> config.chestId)
            .collect(java.util.stream.Collectors.toSet());
        
        assertEquals(expectedChestIds, actualChestIds, "过滤后的宝箱ID应该匹配预期");
        
        // 验证英雄B的专属宝箱被过滤掉
        assertFalse(actualChestIds.contains("chest_003"), "英雄B专属宝箱应该被过滤掉");
    }

    @Test
    @DisplayName("测试无用户偏好的过滤逻辑")
    void testFilteringWithoutUserPreference() {
        // 准备测试数据：用户没有设置偏好
        Map<String, String> userPreferences = new HashMap<>();

        // 准备宝箱配置
        List<MockChestConfig> allChestConfigs = Arrays.asList(
            new MockChestConfig("chest_001", null, null, 1L, 10), // 通用宝箱
            new MockChestConfig("chest_002", "SELECTED_HERO", "HERO_A_001", 2L, 20), // 英雄A专属宝箱（应被过滤）
            new MockChestConfig("chest_003", "SELECTED_HERO", "HERO_B_001", 3L, 30), // 英雄B专属宝箱（应被过滤）
            new MockChestConfig("chest_004", null, null, 4L, 40) // 另一个通用宝箱
        );

        // 执行过滤逻辑
        List<MockChestConfig> filteredConfigs = allChestConfigs.stream()
            .filter(config -> isChestValidForUser(config, userPreferences))
            .toList();

        // 验证过滤结果：只应该保留通用宝箱
        assertEquals(2, filteredConfigs.size(), "应该只保留2个通用宝箱");
        
        Set<String> expectedChestIds = Set.of("chest_001", "chest_004");
        Set<String> actualChestIds = filteredConfigs.stream()
            .map(config -> config.chestId)
            .collect(java.util.stream.Collectors.toSet());
        
        assertEquals(expectedChestIds, actualChestIds, "只应该保留通用宝箱");
    }

    @Test
    @DisplayName("测试宝箱排序逻辑")
    void testChestSorting() {
        // 准备测试数据：已过滤的宝箱配置（乱序）
        List<MockChestConfig> filteredConfigs = Arrays.asList(
            new MockChestConfig("chest_004", null, null, 4L, 40),
            new MockChestConfig("chest_001", null, null, 1L, 10),
            new MockChestConfig("chest_003", null, null, 3L, 30),
            new MockChestConfig("chest_002", null, null, 2L, 20),
            new MockChestConfig("chest_005", null, null, null, 50) // 没有displayOrder的宝箱
        );

        // 执行排序逻辑
        filteredConfigs.sort((a, b) -> {
            Long orderA = a.displayOrder != null ? a.displayOrder : Long.MAX_VALUE;
            Long orderB = b.displayOrder != null ? b.displayOrder : Long.MAX_VALUE;
            return orderA.compareTo(orderB);
        });

        // 验证排序结果
        assertEquals("chest_001", filteredConfigs.get(0).chestId, "第一个应该是displayOrder=1的宝箱");
        assertEquals("chest_002", filteredConfigs.get(1).chestId, "第二个应该是displayOrder=2的宝箱");
        assertEquals("chest_003", filteredConfigs.get(2).chestId, "第三个应该是displayOrder=3的宝箱");
        assertEquals("chest_004", filteredConfigs.get(3).chestId, "第四个应该是displayOrder=4的宝箱");
        assertEquals("chest_005", filteredConfigs.get(4).chestId, "最后一个应该是没有displayOrder的宝箱");
    }

    @Test
    @DisplayName("测试完整的过滤和排序流程")
    void testCompleteFilteringAndSortingFlow() {
        // 准备测试数据
        Map<String, String> userPreferences = new HashMap<>();
        userPreferences.put("SELECTED_HERO", "HERO_A_001");

        List<MockChestConfig> allChestConfigs = Arrays.asList(
            new MockChestConfig("chest_004", null, null, 4L, 40), // 通用宝箱，displayOrder=4
            new MockChestConfig("chest_002", "SELECTED_HERO", "HERO_A_001", 2L, 20), // 英雄A专属，displayOrder=2
            new MockChestConfig("chest_006", "SELECTED_HERO", "HERO_B_001", 6L, 60), // 英雄B专属（应被过滤）
            new MockChestConfig("chest_001", null, null, 1L, 10), // 通用宝箱，displayOrder=1
            new MockChestConfig("chest_005", "SELECTED_HERO", "HERO_A_001", 5L, 50), // 英雄A专属，displayOrder=5
            new MockChestConfig("chest_003", null, null, 3L, 30) // 通用宝箱，displayOrder=3
        );

        // 执行完整的过滤和排序流程
        List<MockChestConfig> result = allChestConfigs.stream()
            .filter(config -> isChestValidForUser(config, userPreferences))
            .sorted((a, b) -> {
                Long orderA = a.displayOrder != null ? a.displayOrder : Long.MAX_VALUE;
                Long orderB = b.displayOrder != null ? b.displayOrder : Long.MAX_VALUE;
                return orderA.compareTo(orderB);
            })
            .toList();

        // 验证最终结果
        assertEquals(5, result.size(), "应该保留5个宝箱（3个通用 + 2个英雄A专属）");
        
        // 验证排序顺序
        List<String> expectedOrder = Arrays.asList("chest_001", "chest_002", "chest_003", "chest_004", "chest_005");
        List<String> actualOrder = result.stream().map(config -> config.chestId).toList();
        
        assertEquals(expectedOrder, actualOrder, "宝箱应该按displayOrder升序排列");
        
        // 验证英雄B专属宝箱被过滤掉
        assertFalse(actualOrder.contains("chest_006"), "英雄B专属宝箱应该被过滤掉");
    }

    /**
     * 宝箱过滤逻辑（复制自实际代码）
     */
    private boolean isChestValidForUser(MockChestConfig chestConfig, Map<String, String> userPreferences) {
        // 通用宝箱：preference_type 为 NULL 或空字符串
        if (chestConfig.preferenceType == null || chestConfig.preferenceType.trim().isEmpty()) {
            return true;
        }

        // 专属宝箱：需要检查用户偏好是否匹配
        String preferenceType = chestConfig.preferenceType;
        String preferenceValue = chestConfig.preferenceValue;
        String userPreferenceValue = userPreferences.get(preferenceType);

        return userPreferenceValue != null && userPreferenceValue.equals(preferenceValue);
    }

    @Test
    @DisplayName("测试状态计算逻辑")
    void testChestStateCalculation() {
        // 测试不同情况下的宝箱状态计算
        
        // 情况1：存在未领取凭证
        String state1 = calculateChestState(true, true, 25, 20);
        assertEquals("UNCLAIMED", state1, "存在未领取凭证时应该返回UNCLAIMED");
        
        // 情况2：已解锁但无凭证（已被领取过）
        String state2 = calculateChestState(false, true, 25, 20);
        assertEquals("CLAIMED", state2, "已解锁但无凭证时应该返回CLAIMED");
        
        // 情况3：未解锁
        String state3 = calculateChestState(false, false, 15, 20);
        assertEquals("LOCKED", state3, "未解锁时应该返回LOCKED");
    }

    /**
     * 状态计算逻辑（简化版）
     */
    private String calculateChestState(boolean hasEntitlement, boolean isUnlocked, long currentProgress, int unlockProgress) {
        // 存在未领取凭证
        if (hasEntitlement) {
            return "UNCLAIMED";
        }

        // 检查是否已解锁
        boolean actuallyUnlocked = currentProgress >= unlockProgress;
        
        if (actuallyUnlocked) {
            // 已解锁但无凭证，说明已被领取过
            return "CLAIMED";
        } else {
            // 未解锁
            return "LOCKED";
        }
    }
}
