package com.kikitrade.activity.service.remote.impl;

import com.kikitrade.activity.api.model.request.reward.LotteryStateRequest;
import com.kikitrade.activity.api.model.response.reward.LotteryStateResponse;
import com.kikitrade.activity.dal.tablestore.builder.*;
import com.kikitrade.activity.dal.tablestore.model.*;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.draw.UnifiedClaimService;
import com.kikitrade.activity.service.draw.preference.UserPreferenceService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 测试修改后的抽奖状态查询业务流程
 * 验证用户偏好过滤和宝箱排序逻辑
 */
@ExtendWith(MockitoExtension.class)
class RemoteLotteryServiceImplTest {

    @Mock
    private UserPreferenceBuilder userPreferenceBuilder;
    
    @Mock
    private ProgressChestConfigBuilder progressChestConfigBuilder;
    
    @Mock
    private UserProgressTrackerBuilder userProgressTrackerBuilder;
    
    @Mock
    private UserClaimEntitlementBuilder userClaimEntitlementBuilder;
    
    @Mock
    private PrizePoolBuilder prizePoolBuilder;
    
    @Mock
    private UserPreferenceService userPreferenceService;
    
    @Mock
    private UnifiedClaimService unifiedClaimService;

    @InjectMocks
    private RemoteLotteryServiceImpl remoteLotteryService;

    private String testUserId = "test_user_001";
    private String testSaasId = "mugen";
    private String testPrizePoolCode = "OBT2_HERO_WHEEL";

    @BeforeEach
    void setUp() {
        // 设置基础的mock行为
    }

    @Test
    void testGetLotteryState_WithUserPreferenceFiltering() {
        // 准备测试数据
        
        // 1. 模拟用户偏好：用户选择了英雄A
        Map<String, String> userPreferences = new HashMap<>();
        userPreferences.put("SELECTED_HERO", "HERO_A_001");
        when(userPreferenceBuilder.getUserPreferenceMap(testUserId, testPrizePoolCode))
            .thenReturn(userPreferences);

        // 2. 模拟宝箱配置：包含通用宝箱和不同英雄的专属宝箱
        List<ProgressChestConfig> allChestConfigs = Arrays.asList(
            createChestConfig("chest_001", null, null, 1L, 10), // 通用宝箱
            createChestConfig("chest_002", "SELECTED_HERO", "HERO_A_001", 2L, 20), // 英雄A专属宝箱
            createChestConfig("chest_003", "SELECTED_HERO", "HERO_B_001", 3L, 30), // 英雄B专属宝箱（应被过滤）
            createChestConfig("chest_004", null, null, 4L, 40) // 另一个通用宝箱
        );
        when(progressChestConfigBuilder.findBySaasIdAndPrizePoolCode(testSaasId, testPrizePoolCode))
            .thenReturn(allChestConfigs);

        // 3. 模拟用户进度
        UserProgressTracker progressTracker = new UserProgressTracker();
        progressTracker.setUserId(testUserId);
        progressTracker.setPrizePoolCode(testPrizePoolCode);
        progressTracker.setCurrentProgress(25); // 当前进度25，可以解锁前两个宝箱
        progressTracker.setCycleStartTime(System.currentTimeMillis());
        when(userProgressTrackerBuilder.findByUserIdAndPrizePoolCode(testUserId, testPrizePoolCode))
            .thenReturn(progressTracker);

        // 4. 模拟奖池信息
        PrizePool prizePool = new PrizePool();
        prizePool.setCode(testPrizePoolCode);
        prizePool.setName("测试奖池");
        prizePool.setChestCycleDays(7L);
        when(prizePoolBuilder.findByCodeAndSaasId(testPrizePoolCode, testSaasId))
            .thenReturn(prizePool);

        // 5. 模拟用户偏好服务
        when(userPreferenceService.getAllUserPreferences(testUserId, testPrizePoolCode))
            .thenReturn(userPreferences);

        // 6. 模拟领取凭证查询（假设第二个宝箱有未领取凭证）
        UserClaimEntitlement entitlement = new UserClaimEntitlement();
        entitlement.setClaimId("claim_001");
        entitlement.setUserId(testUserId);
        entitlement.setRewardSourceId("chest_002");
        when(userClaimEntitlementBuilder.findUnClaimedChest(eq(testUserId), eq("chest_002"), anyLong()))
            .thenReturn(entitlement);

        // 执行测试
        LotteryStateRequest request = new LotteryStateRequest();
        request.setUserId(testUserId);
        request.setSaasId(testSaasId);
        request.setPrizePoolCode(testPrizePoolCode);

        LotteryStateResponse response = remoteLotteryService.getLotteryState(request);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.getSuccess());
        assertNotNull(response.getData());
        
        List<LotteryStateResponse.ChestInfo> chests = response.getData().getChests();
        assertNotNull(chests);
        
        // 验证过滤逻辑：应该只返回3个宝箱（2个通用 + 1个英雄A专属），英雄B专属宝箱被过滤掉
        assertEquals(3, chests.size());
        
        // 验证排序逻辑：按display_order升序排列
        assertEquals("chest_001", chests.get(0).getChestId()); // display_order = 1
        assertEquals("chest_002", chests.get(1).getChestId()); // display_order = 2
        assertEquals("chest_004", chests.get(2).getChestId()); // display_order = 4
        
        // 验证状态逻辑
        assertEquals(ActivityConstant.EntitlementStatusEnum.CLAIMED.name(), chests.get(0).getState()); // 已解锁但无凭证
        assertEquals(ActivityConstant.EntitlementStatusEnum.UNCLAIMED.name(), chests.get(1).getState()); // 有未领取凭证
        assertEquals("claim_001", chests.get(1).getClaimId());
        assertEquals(ActivityConstant.EntitlementStatusEnum.LOCKED.name(), chests.get(2).getState()); // 未解锁
    }

    @Test
    void testGetLotteryState_WithoutUserPreference() {
        // 测试用户没有设置偏好的情况，应该只返回通用宝箱
        
        // 1. 模拟空的用户偏好
        when(userPreferenceBuilder.getUserPreferenceMap(testUserId, testPrizePoolCode))
            .thenReturn(new HashMap<>());

        // 2. 模拟宝箱配置
        List<ProgressChestConfig> allChestConfigs = Arrays.asList(
            createChestConfig("chest_001", null, null, 1L, 10), // 通用宝箱
            createChestConfig("chest_002", "SELECTED_HERO", "HERO_A_001", 2L, 20), // 英雄A专属宝箱（应被过滤）
            createChestConfig("chest_003", "SELECTED_HERO", "HERO_B_001", 3L, 30) // 英雄B专属宝箱（应被过滤）
        );
        when(progressChestConfigBuilder.findBySaasIdAndPrizePoolCode(testSaasId, testPrizePoolCode))
            .thenReturn(allChestConfigs);

        // 3. 其他必要的mock设置
        setupBasicMocks();

        // 执行测试
        LotteryStateRequest request = new LotteryStateRequest();
        request.setUserId(testUserId);
        request.setSaasId(testSaasId);
        request.setPrizePoolCode(testPrizePoolCode);

        LotteryStateResponse response = remoteLotteryService.getLotteryState(request);

        // 验证结果：只应该返回通用宝箱
        assertNotNull(response);
        assertTrue(response.getSuccess());
        
        List<LotteryStateResponse.ChestInfo> chests = response.getData().getChests();
        assertEquals(1, chests.size());
        assertEquals("chest_001", chests.get(0).getChestId());
    }

    /**
     * 创建测试用的宝箱配置
     */
    private ProgressChestConfig createChestConfig(String chestId, String preferenceType, 
                                                String preferenceValue, Long displayOrder, Integer unlockProgress) {
        ProgressChestConfig config = new ProgressChestConfig();
        config.setSaasId(testSaasId);
        config.setPrizePoolCode(testPrizePoolCode);
        config.setChestId(chestId);
        config.setPreferenceType(preferenceType);
        config.setPreferenceValue(preferenceValue);
        config.setDisplayOrder(displayOrder);
        config.setUnlockProgress(unlockProgress);
        config.setChestName("测试宝箱_" + chestId);
        config.setIconUrl("http://example.com/icon_" + chestId + ".png");
        config.setIsActive(true);
        return config;
    }

    /**
     * 设置基础的mock行为
     */
    private void setupBasicMocks() {
        // 模拟用户进度
        UserProgressTracker progressTracker = new UserProgressTracker();
        progressTracker.setCurrentProgress(15);
        progressTracker.setCycleStartTime(System.currentTimeMillis());
        when(userProgressTrackerBuilder.findByUserIdAndPrizePoolCode(testUserId, testPrizePoolCode))
            .thenReturn(progressTracker);

        // 模拟奖池信息
        PrizePool prizePool = new PrizePool();
        prizePool.setCode(testPrizePoolCode);
        prizePool.setName("测试奖池");
        prizePool.setChestCycleDays(7L);
        when(prizePoolBuilder.findByCodeAndSaasId(testPrizePoolCode, testSaasId))
            .thenReturn(prizePool);

        // 模拟用户偏好服务
        when(userPreferenceService.getAllUserPreferences(testUserId, testPrizePoolCode))
            .thenReturn(new HashMap<>());
    }
}
