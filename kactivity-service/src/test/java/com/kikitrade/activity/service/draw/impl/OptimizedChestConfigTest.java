package com.kikitrade.activity.service.draw.impl;

import com.alibaba.fastjson2.JSON;
import com.kikitrade.activity.dal.tablestore.model.ProgressChestConfig;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.domain.PreferenceMapping;
import com.kikitrade.activity.service.draw.RewardIssueService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 优化后的宝箱配置测试
 * 验证新的 reward_type、quantity 字段和 extra_properties 偏好映射功能
 */
@ExtendWith(MockitoExtension.class)
class OptimizedChestConfigTest {

    @Mock
    private com.kikitrade.activity.dal.tablestore.builder.ProgressChestConfigBuilder progressChestConfigBuilder;
    
    @Mock
    private com.kikitrade.activity.dal.tablestore.builder.ItemMasterConfigBuilder itemMasterConfigBuilder;

    @InjectMocks
    private RewardIssueServiceImpl rewardIssueService;

    private String testUserId = "test_user_001";
    private String testSaasId = "mugen";

    @BeforeEach
    void setUp() {
        // 设置基础的mock行为
    }

    @Test
    @DisplayName("测试道具类型宝箱配置解析")
    void testItemTypeChestConfig() {
        // 创建道具类型的宝箱配置
        ProgressChestConfig chestConfig = createItemTypeChestConfig();
        
        // 验证基本字段
        assertEquals("ITEM", chestConfig.getRewardType());
        assertEquals("gold", chestConfig.getPackIdOnUnlock());
        assertEquals(Integer.valueOf(100), chestConfig.getQuantity());
        
        // 验证奖励类型枚举
        ActivityConstant.ChestRewardTypeEnum rewardType = ActivityConstant.ChestRewardTypeEnum.fromCode(chestConfig.getRewardType());
        assertEquals(ActivityConstant.ChestRewardTypeEnum.ITEM, rewardType);
    }

    @Test
    @DisplayName("测试礼包类型宝箱配置解析")
    void testGiftPackTypeChestConfig() {
        // 创建礼包类型的宝箱配置
        ProgressChestConfig chestConfig = createGiftPackTypeChestConfig();
        
        // 验证基本字段
        assertEquals("GIFT_PACK", chestConfig.getRewardType());
        assertEquals("pack_005", chestConfig.getPackIdOnUnlock());
        assertNull(chestConfig.getQuantity());
        
        // 验证奖励类型枚举
        ActivityConstant.ChestRewardTypeEnum rewardType = ActivityConstant.ChestRewardTypeEnum.fromCode(chestConfig.getRewardType());
        assertEquals(ActivityConstant.ChestRewardTypeEnum.GIFT_PACK, rewardType);
    }

    @Test
    @DisplayName("测试偏好映射解析")
    void testPreferenceMappingParsing() {
        // 创建包含偏好映射的宝箱配置
        ProgressChestConfig chestConfig = createItemTypeChestConfig();
        
        // 解析偏好映射
        PreferenceMapping preferenceMapping = JSON.parseObject(chestConfig.getExtraProperties(), PreferenceMapping.class);
        
        assertNotNull(preferenceMapping);
        assertNotNull(preferenceMapping.getPreferenceMapping());
        
        // 验证 SELECTED_HERO 偏好类型存在
        assertTrue(preferenceMapping.getPreferenceMapping().containsKey("SELECTED_HERO"));
        
        Map<String, PreferenceMapping.RewardConfig> heroMapping = preferenceMapping.getPreferenceMapping().get("SELECTED_HERO");
        assertNotNull(heroMapping);
        
        // 验证具体的英雄映射
        assertTrue(heroMapping.containsKey("HERO_A"));
        PreferenceMapping.RewardConfig heroAConfig = heroMapping.get("HERO_A");
        assertNotNull(heroAConfig);
        assertEquals("gold", heroAConfig.getItemId());
        assertEquals(Integer.valueOf(100), heroAConfig.getQuantity());
        assertTrue(heroAConfig.isItemReward());
        assertFalse(heroAConfig.isGiftPackReward());
        
        // 验证其他英雄映射
        assertTrue(heroMapping.containsKey("HERO_B"));
        PreferenceMapping.RewardConfig heroBConfig = heroMapping.get("HERO_B");
        assertEquals("exp_potion", heroBConfig.getItemId());
        assertEquals(Integer.valueOf(50), heroBConfig.getQuantity());
    }

    @Test
    @DisplayName("测试礼包类型的偏好映射解析")
    void testGiftPackPreferenceMappingParsing() {
        // 创建礼包类型的宝箱配置
        ProgressChestConfig chestConfig = createGiftPackTypeChestConfig();
        
        // 解析偏好映射
        PreferenceMapping preferenceMapping = JSON.parseObject(chestConfig.getExtraProperties(), PreferenceMapping.class);
        
        assertNotNull(preferenceMapping);
        Map<String, PreferenceMapping.RewardConfig> heroMapping = preferenceMapping.getPreferenceMapping().get("SELECTED_HERO");
        
        // 验证礼包映射
        PreferenceMapping.RewardConfig heroAConfig = heroMapping.get("HERO_A");
        assertNotNull(heroAConfig);
        assertEquals("pack_005_A", heroAConfig.getPackId());
        assertFalse(heroAConfig.isItemReward());
        assertTrue(heroAConfig.isGiftPackReward());
    }

    @Test
    @DisplayName("测试配置量优化效果")
    void testConfigurationOptimization() {
        // 原设计：每个宝箱需要 9 条记录（9种偏好值）
        // 新设计：每个宝箱只需要 1 条记录，偏好映射存储在 extra_properties 中
        
        // 模拟 3 个宝箱的配置
        List<ProgressChestConfig> optimizedConfigs = Arrays.asList(
            createItemTypeChestConfig(),
            createGiftPackTypeChestConfig(),
            createUltimateChestConfig()
        );
        
        // 验证配置数量
        assertEquals(3, optimizedConfigs.size(), "优化后应该只有3条宝箱配置记录");
        
        // 验证每个配置都包含完整的偏好映射
        for (ProgressChestConfig config : optimizedConfigs) {
            assertNotNull(config.getExtraProperties(), "每个配置都应该包含偏好映射");
            
            PreferenceMapping mapping = JSON.parseObject(config.getExtraProperties(), PreferenceMapping.class);
            assertNotNull(mapping.getPreferenceMapping());
            
            Map<String, PreferenceMapping.RewardConfig> heroMapping = mapping.getPreferenceMapping().get("SELECTED_HERO");
            assertNotNull(heroMapping);
            assertEquals(9, heroMapping.size(), "每个宝箱应该支持9种英雄偏好");
        }
    }

    @Test
    @DisplayName("测试RewardConfig工厂方法")
    void testRewardConfigFactoryMethods() {
        // 测试道具奖励配置创建
        PreferenceMapping.RewardConfig itemConfig = PreferenceMapping.RewardConfig.createItemReward("gold", 100);
        assertEquals("gold", itemConfig.getItemId());
        assertEquals(Integer.valueOf(100), itemConfig.getQuantity());
        assertTrue(itemConfig.isItemReward());
        assertFalse(itemConfig.isGiftPackReward());
        
        // 测试礼包奖励配置创建
        PreferenceMapping.RewardConfig packConfig = PreferenceMapping.RewardConfig.createGiftPackReward("pack_001");
        assertEquals("pack_001", packConfig.getPackId());
        assertFalse(packConfig.isItemReward());
        assertTrue(packConfig.isGiftPackReward());
    }

    /**
     * 创建道具类型的宝箱配置
     */
    private ProgressChestConfig createItemTypeChestConfig() {
        ProgressChestConfig config = new ProgressChestConfig();
        config.setSaasId(testSaasId);
        config.setPrizePoolCode("ADVANCED_POOL");
        config.setChestId("chest_004");
        config.setRewardType("ITEM");
        config.setPackIdOnUnlock("gold");
        config.setQuantity(100);
        config.setUnlockProgress(5000);
        config.setDisplayOrder(1L);
        config.setChestName("Advanced Item Chest");
        config.setIsActive(true);
        
        // 创建偏好映射
        Map<String, PreferenceMapping.RewardConfig> heroMapping = new HashMap<>();
        heroMapping.put("HERO_A", PreferenceMapping.RewardConfig.createItemReward("gold", 100));
        heroMapping.put("HERO_B", PreferenceMapping.RewardConfig.createItemReward("exp_potion", 50));
        heroMapping.put("HERO_C", PreferenceMapping.RewardConfig.createItemReward("gem", 20));
        heroMapping.put("HERO_D", PreferenceMapping.RewardConfig.createItemReward("gold", 120));
        heroMapping.put("HERO_E", PreferenceMapping.RewardConfig.createItemReward("exp_potion", 60));
        heroMapping.put("HERO_F", PreferenceMapping.RewardConfig.createItemReward("gem", 25));
        heroMapping.put("HERO_G", PreferenceMapping.RewardConfig.createItemReward("gold", 150));
        heroMapping.put("HERO_H", PreferenceMapping.RewardConfig.createItemReward("exp_potion", 80));
        heroMapping.put("HERO_I", PreferenceMapping.RewardConfig.createItemReward("gem", 30));
        
        Map<String, Map<String, PreferenceMapping.RewardConfig>> preferenceMapping = new HashMap<>();
        preferenceMapping.put("SELECTED_HERO", heroMapping);
        
        PreferenceMapping mapping = new PreferenceMapping(preferenceMapping);
        config.setExtraProperties(JSON.toJSONString(mapping));
        
        return config;
    }

    /**
     * 创建礼包类型的宝箱配置
     */
    private ProgressChestConfig createGiftPackTypeChestConfig() {
        ProgressChestConfig config = new ProgressChestConfig();
        config.setSaasId(testSaasId);
        config.setPrizePoolCode("ADVANCED_POOL");
        config.setChestId("chest_005");
        config.setRewardType("GIFT_PACK");
        config.setPackIdOnUnlock("pack_005");
        config.setQuantity(null);
        config.setUnlockProgress(10000);
        config.setDisplayOrder(2L);
        config.setChestName("Advanced Gift Pack Chest");
        config.setIsActive(true);
        
        // 创建偏好映射
        Map<String, PreferenceMapping.RewardConfig> heroMapping = new HashMap<>();
        heroMapping.put("HERO_A", PreferenceMapping.RewardConfig.createGiftPackReward("pack_005_A"));
        heroMapping.put("HERO_B", PreferenceMapping.RewardConfig.createGiftPackReward("pack_005_B"));
        heroMapping.put("HERO_C", PreferenceMapping.RewardConfig.createGiftPackReward("pack_005_C"));
        heroMapping.put("HERO_D", PreferenceMapping.RewardConfig.createGiftPackReward("pack_005_D"));
        heroMapping.put("HERO_E", PreferenceMapping.RewardConfig.createGiftPackReward("pack_005_E"));
        heroMapping.put("HERO_F", PreferenceMapping.RewardConfig.createGiftPackReward("pack_005_F"));
        heroMapping.put("HERO_G", PreferenceMapping.RewardConfig.createGiftPackReward("pack_005_G"));
        heroMapping.put("HERO_H", PreferenceMapping.RewardConfig.createGiftPackReward("pack_005_H"));
        heroMapping.put("HERO_I", PreferenceMapping.RewardConfig.createGiftPackReward("pack_005_I"));
        
        Map<String, Map<String, PreferenceMapping.RewardConfig>> preferenceMapping = new HashMap<>();
        preferenceMapping.put("SELECTED_HERO", heroMapping);
        
        PreferenceMapping mapping = new PreferenceMapping(preferenceMapping);
        config.setExtraProperties(JSON.toJSONString(mapping));
        
        return config;
    }

    /**
     * 创建终极宝箱配置
     */
    private ProgressChestConfig createUltimateChestConfig() {
        ProgressChestConfig config = new ProgressChestConfig();
        config.setSaasId(testSaasId);
        config.setPrizePoolCode("ADVANCED_POOL");
        config.setChestId("chest_006");
        config.setRewardType("ITEM");
        config.setPackIdOnUnlock("diamond");
        config.setQuantity(10);
        config.setUnlockProgress(20000);
        config.setDisplayOrder(3L);
        config.setChestName("Ultimate Reward Chest");
        config.setIsActive(true);
        
        // 创建偏好映射
        Map<String, PreferenceMapping.RewardConfig> heroMapping = new HashMap<>();
        heroMapping.put("HERO_A", PreferenceMapping.RewardConfig.createItemReward("diamond", 10));
        heroMapping.put("HERO_B", PreferenceMapping.RewardConfig.createItemReward("rare_gem", 5));
        heroMapping.put("HERO_C", PreferenceMapping.RewardConfig.createItemReward("epic_crystal", 3));
        heroMapping.put("HERO_D", PreferenceMapping.RewardConfig.createItemReward("diamond", 12));
        heroMapping.put("HERO_E", PreferenceMapping.RewardConfig.createItemReward("rare_gem", 6));
        heroMapping.put("HERO_F", PreferenceMapping.RewardConfig.createItemReward("epic_crystal", 4));
        heroMapping.put("HERO_G", PreferenceMapping.RewardConfig.createItemReward("diamond", 15));
        heroMapping.put("HERO_H", PreferenceMapping.RewardConfig.createItemReward("rare_gem", 8));
        heroMapping.put("HERO_I", PreferenceMapping.RewardConfig.createItemReward("epic_crystal", 5));
        
        Map<String, Map<String, PreferenceMapping.RewardConfig>> preferenceMapping = new HashMap<>();
        preferenceMapping.put("SELECTED_HERO", heroMapping);
        
        PreferenceMapping mapping = new PreferenceMapping(preferenceMapping);
        config.setExtraProperties(JSON.toJSONString(mapping));
        
        return config;
    }
}
