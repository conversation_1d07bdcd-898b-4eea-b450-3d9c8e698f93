<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kactivity</artifactId>
        <groupId>com.kikitrade</groupId>
        <version>3.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.kikitrade</groupId>
    <artifactId>kactivity-service</artifactId>
    <version>3.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <properties>
        <!-- 指定项目是JDK1.7版本 -->
        <java-version>17</java-version>
        <!--指定Maven用什么编码来读取源码及文档 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!--指定Maven用什么编码来呈现 -->
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <web3j.version>4.10.3</web3j.version>
        <web3j.out.path>src/main/generated/</web3j.out.path>
        <com.alibaba.nacos.version>2.0.3</com.alibaba.nacos.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kactivity-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kactivity-dal</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kactivity-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-ots-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- web项目需要的jar
        <dependency>
            <groupId>javax.servlet.jsp</groupId>
            <artifactId>jsp-api</artifactId>
            <scope>provided</scope>
        </dependency>-->

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        <!-- spring orm -->
        <!--<dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
        </dependency>-->
        <!-- https://mvnrepository.com/artifact/org.jeasy/easy-rules-core -->
        <dependency>
            <groupId>org.jeasy</groupId>
            <artifactId>easy-rules-core</artifactId>
            <version>3.1.0</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.reflections</groupId>-->
<!--            <artifactId>reflections-maven</artifactId>-->
<!--            <version>0.9.8</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-x-discovery</artifactId>
        </dependency>

        <!-- grpc -->
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-netty-shaded</artifactId>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-protobuf</artifactId>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-stub</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>io.grpc</groupId>-->
<!--            <artifactId>grpc-services</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
            <version>3.7.1</version>
        </dependency>

        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-dubbo-deployment-tag-aware-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-observability-tracing-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-observability-metrics-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.javacsv</groupId>
            <artifactId>javacsv</artifactId>
            <version>2.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <version>${com.alibaba.nacos.version}</version>
        </dependency>

        <!-- session -->
<!--        <dependency>-->
<!--            <groupId>org.springframework.session</groupId>-->
<!--            <artifactId>spring-session-data-redis</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>

        <!-- oss使用aliyun-core 3.4.0不能升级为最新版本 -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>31.0.1-jre</version>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>ksocial-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
        </dependency>

        <dependency>
            <groupId>com.twitter</groupId>
            <artifactId>twitter-api-java-sdk</artifactId>
            <version>2.0.3</version>
        </dependency>
        <dependency>
            <groupId>org.telegram</groupId>
            <artifactId>telegrambots</artifactId>
            <version>6.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.kikitrade.knotify</groupId>
            <artifactId>knotify-api</artifactId>
            <version>2.1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.web3j</groupId>
            <artifactId>core</artifactId>
            <version>4.10.3</version> <!-- 请使用最新版本 -->
        </dependency>

    </dependencies>
    <build>
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.7.1</version>
            </extension>
        </extensions>
        <plugins>
            <plugin>
                <groupId>org.web3j</groupId>
                <artifactId>web3j-maven-plugin</artifactId>
                <version>${web3j.version}</version>
                <configuration>
                    <packageName>com.kikitarde.contract.generated</packageName>
                    <nativeJavaType>true</nativeJavaType>
                    <abiSourceFiles>
                        <directory>src/main/resources/abis</directory>
                        <includes>
                            <include>**/*.json</include>
                        </includes>
                    </abiSourceFiles>
                    <outputDirectory>
                        <java>${web3j.out.path}</java>
                    </outputDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/main/generated</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.xolstice.maven.plugins</groupId>
                <artifactId>protobuf-maven-plugin</artifactId>
                <version>0.6.1</version>
                <configuration>
                    <protocArtifact>com.google.protobuf:protoc:3.24.3:exe:${os.detected.classifier}</protocArtifact>
                    <pluginId>grpc-java</pluginId>
                    <pluginArtifact>io.grpc:protoc-gen-grpc-java:1.58.0:exe:${os.detected.classifier}</pluginArtifact>
                    <protocPlugins>
                        <protocPlugin>
                            <id>dubbo</id>
                            <groupId>org.apache.dubbo</groupId>`
                            <artifactId>dubbo-compiler</artifactId>
                            <version>3.2.7</version>
                            <mainClass>org.apache.dubbo.gen.tri.Dubbo3TripleGenerator</mainClass>
                        </protocPlugin>
                    </protocPlugins>
                    <protoSourceRoot>${basedir}/src/main/resources/proto/kactivity-facade/kactivity</protoSourceRoot>
                    <outputDirectory>${basedir}/src/main/generated</outputDirectory>
                    <clearOutputDirectory>false</clearOutputDirectory>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>compile-custom</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
