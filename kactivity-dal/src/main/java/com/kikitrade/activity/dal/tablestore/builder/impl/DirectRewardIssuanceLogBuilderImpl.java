package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.kikitrade.activity.dal.tablestore.builder.DirectRewardIssuanceLogBuilder;
import com.kikitrade.activity.dal.tablestore.model.DirectRewardIssuanceLog;
import com.kikitrade.activity.dal.tablestore.model.PrizeConfig;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

import static com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward.INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX;
import static com.kikitrade.activity.dal.tablestore.model.DirectRewardIssuanceLog.SEARCH_DIRECT_REWARD_ISSUANCE_LOG;

/**
 * <AUTHOR>
 * @date 2025/9/4 10:24
 * @description:
 */
@Slf4j
@Component
public class DirectRewardIssuanceLogBuilderImpl extends WideColumnStoreBuilder<DirectRewardIssuanceLog> implements DirectRewardIssuanceLogBuilder {

    @Override
    public String getTableName() {
        return "direct_reward_issuance_log";
    }

    @PostConstruct
    public void init() {
        super.init(DirectRewardIssuanceLog.class);
    }

    /**
     * 根据交易ID查询发放记录（用于幂等性检查）
     *
     * @param transactionId 交易ID
     * @return 发放记录
     */
    @Override
    public DirectRewardIssuanceLog findByTransactionId(String transactionId) {
        DirectRewardIssuanceLog log = new DirectRewardIssuanceLog();
        log.setTransactionId(transactionId);

        return getRow(log);
    }

    /**
     * 根据用户ID查询发放记录
     *
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param limit  查询限制
     * @return 发放记录列表
     */
    @Override
    public TokenPage<DirectRewardIssuanceLog> findByUserId(String userId, String nextToken, int limit) {

        BoolQuery boolQuery = QueryBuilders.bool()
            .must(QueryBuilders.term("user_id", userId))
            .build();
        Sort sort = new Sort(Collections.singletonList(new FieldSort("create_time", SortOrder.DESC)));
        return pageSearchQuery(boolQuery, sort, nextToken, limit, SEARCH_DIRECT_REWARD_ISSUANCE_LOG);
    }

    /**
     * 根据渠道查询发放记录
     *
     * @param channel 渠道
     * @param saasId  SaaS ID
     * @param limit   查询限制
     * @return 发放记录列表
     */
    @Override
    public List<DirectRewardIssuanceLog> findByChannel(String channel, String saasId, int limit) {
        return List.of();
    }

    /**
     * 根据发放状态查询记录
     *
     * @param issueStatus 发放状态
     * @param saasId      SaaS ID
     * @param limit       查询限制
     * @return 发放记录列表
     */
    @Override
    public List<DirectRewardIssuanceLog> findByIssueStatus(String issueStatus, String saasId, int limit) {
        return List.of();
    }

    /**
     * 插入发放记录
     *
     * @param log 发放记录
     * @return 是否成功
     */
    @Override
    public boolean insert(DirectRewardIssuanceLog log) {
        return super.putRow(log, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    /**
     * 更新发放记录
     *
     * @param log 发放记录
     * @return 是否成功
     */
    @Override
    public boolean update(DirectRewardIssuanceLog log) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return super.updateRow(log, condition);
    }
}
