package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.UserClaimEntitlement;

import java.util.List;

/**
 * 用户领奖凭证表数据访问层接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface UserClaimEntitlementBuilder {

    String getTableName();
    /**
     * 保存用户领奖凭证（插入或更新）
     *
     * @param entitlement 用户领奖凭证
     * @return 是否成功
     */
    boolean save(UserClaimEntitlement entitlement);

    /**
     * 插入用户领奖凭证
     *
     * @param entitlement 用户领奖凭证
     * @return 是否成功
     */
    boolean insert(UserClaimEntitlement entitlement);

    /**
     * 更新用户领奖凭证
     *
     * @param entitlement 用户领奖凭证
     * @return 是否成功
     */
    boolean update(UserClaimEntitlement entitlement);

    /**
     * 根据用户ID查询未领取的凭证
     *
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @return 未领取的凭证列表
     */
    List<UserClaimEntitlement> findUnclaimedByUserId(String userId);

    /**
     * 根据用户ID和凭证ID查询凭证
     *
     * @param userId 用户ID
     * @param claimId 凭证ID
     * @param saasId SaaS ID
     * @return 凭证信息
     */
    UserClaimEntitlement findByUserIdAndClaimId(String userId, String claimId);

    /**
     * 根据来源交易ID查询凭证（用于幂等性检查）
     *
     * @param sourceTransactionId 来源交易ID
     * @param saasId SaaS ID
     * @return 凭证信息
     */
    UserClaimEntitlement findBySourceTransactionId(String sourceTransactionId, String saasId);

    /**
     * 查询过期的凭证
     *
     * @param currentTime 当前时间
     * @param saasId SaaS ID
     * @return 过期的凭证列表
     */
    List<UserClaimEntitlement> findExpiredEntitlements(Long currentTime, String saasId);

    /**
     * 根据用户ID查询所有凭证
     *
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @return 凭证列表
     */
    List<UserClaimEntitlement> findByUserId(String userId);

    /**
     * 根据用户ID和奖励类型查询凭证
     *
     * @param userId 用户ID
     * @param rewardType 奖励类型
     * @param saasId SaaS ID
     * @return 凭证列表
     */
    List<UserClaimEntitlement> findByUserIdAndRewardType(String userId, String rewardType);

    /**
     * 根据用户ID和状态查询凭证
     *
     * @param userId 用户ID
     * @param status 状态
     * @param saasId SaaS ID
     * @return 凭证列表
     */
    List<UserClaimEntitlement> findByUserIdAndStatus(String userId, String status);

    /**
     * 删除凭证
     *
     * @param userId 用户ID
     * @param claimId 凭证ID
     * @return 是否成功
     */
    boolean delete(String userId, String claimId);

    UserClaimEntitlement findUnClaimedChest(String userId, String chestId, Long cycleStartTime);
}
