package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.PrizePool;

import java.util.List;

/**
 * 奖池配置表数据访问层接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface PrizePoolBuilder {

    String getTableName();

    /**
     * 根据奖池编码查询奖池配置
     */
    PrizePool findByCode(String code);

    /**
     * 根据SaaS ID查询所有活跃的奖池
     */
    List<PrizePool> findActiveBySaasId(String saasId);

    /**
     * 根据编码和SaaS ID查询奖池
     */
    PrizePool findByCodeAndSaasId(String code, String saasId);

    /**
     * 插入奖池配置
     */
    boolean insert(PrizePool prizePool);

    /**
     * 更新奖池配置
     */
    boolean update(PrizePool prizePool);

    /**
     * 根据时间范围查询活跃的奖池
     */
    List<PrizePool> findActiveByTimeRange(long startTime, long endTime, String saasId);

}