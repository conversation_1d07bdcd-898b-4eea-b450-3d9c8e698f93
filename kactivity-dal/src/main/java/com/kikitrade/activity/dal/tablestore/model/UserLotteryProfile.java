package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户抽奖档案表
 * 对应技术规格书中的 user_lottery_profile 表
 * 用于实现多维度风控和英雄选择管理
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Table(name = "user_lottery_profile")
public class UserLotteryProfile implements Serializable {

    public static final String SEARCH_USER_LOTTERY_PROFILE = "search_user_lottery_profile";

    /**
     * 用户ID（主键）
     */
    @PartitionKey(name = "user_id")
    @SearchIndex(name = SEARCH_USER_LOTTERY_PROFILE, column = "user_id")
    private String userId;

    /**
     * 历史总抽奖次数
     */
    @Column(name = "total_draw_count", type = Column.Type.INTEGER)
    private Long totalDrawCount;

    /**
     * 历史总中奖次数
     */
    @Column(name = "total_win_count", type = Column.Type.INTEGER)
    private Integer totalWinCount;

    /**
     * 今日已抽奖次数
     */
    @Column(name = "daily_draw_count", type = Column.Type.INTEGER)
    private Integer dailyDrawCount;

    /**
     * 本周已抽奖次数
     */
    @Column(name = "weekly_draw_count", type = Column.Type.INTEGER)
    private Integer weeklyDrawCount;

    /**
     * 本月已抽奖次数
     */
    @Column(name = "monthly_draw_count", type = Column.Type.INTEGER)
    private Integer monthlyDrawCount;

    /**
     * 上次抽奖时间
     */
    @Column(name = "last_draw_time", type = Column.Type.INTEGER)
    private Long lastDrawTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = Column.Type.INTEGER)
    private Long updateTime;

}