package com.kikitrade.activity.dal.tablestore.model;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.alicloud.openservices.tablestore.model.search.FieldSchema;
import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.domain.GroupAward;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/20 11:22
 */
@Data
@Table(name = "task_config")
public class TaskConfig implements Serializable {

    public static final String SEARCH_TASK_CONFIG = "search_task_config";

    @PartitionKey(name = "task_id")
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "task_id")
    private String taskId;

    @Column(name = "group_id", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "group_id")
    private String groupId;

    @Column(name = "show_list", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "show_list", fieldType = FieldType.BOOLEAN)
    private Boolean showList;

    @Column(name = "title", isDefined = true)
    private String title;

    @Column(name = "desc", isDefined = true)
    private String desc;

    @Column(name = "label_name")
    private String labelName;

    @Column(name = "label_color")
    private String labelColor;

    /**
     * 任务图片
     */
    @Column(name = "image", isDefined = true)
    private String image;

    @Column(name = "saasId", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "saasId")
    private String saasId;

    /**
     * 任务状态
     * @see ActivityConstant.CommonStatus
     */
    @Column(name = "status", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "status")
    private String status;

    @Column(name = "start_time", isDefined = true, type = Column.Type.INTEGER)
    private Date startTime;

    @Column(name = "end_time", isDefined = true, type = Column.Type.INTEGER)
    private Date endTime;

    @Column(name = "code", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "code")
    private String code;

    @Column(name = "show_code")
    private String showCode;

    /**
     * 周期任务上线
     */
    @Column(name = "limit", isDefined = true, type = Column.Type.INTEGER)
    private Integer limit;

    @Column(name = "limit_map", isDefined = true, type = Column.Type.STRING)
    private String limitMap;

    /**
     * 奖励频率
     */
    @Column(name = "reward_frequency", isDefined = true, type = Column.Type.INTEGER)
    private Integer rewardFrequency;

    /**
     * 任务周期
     * @see ActivityTaskConstant.TaskCycleEnum
     */
    @Column(name = "cycle", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "cycle")
    private String cycle;

    /**
     * 任务进度计算方式
     * @see ActivityTaskConstant.ProgressTypeEnum
     */
    @Column(name = "progress_type", isDefined = true)
    private String progressType;

    /**
     * 奖励方式
     * @see ActivityTaskConstant.RewardForm
     */
    @Column(name = "reward_form", isDefined = true)
    private String rewardForm;

    /**
     * 奖励方式
     * @see ActivityTaskConstant.ProvideType
     */
    @Column(name = "provide_type", isDefined = true)
    private String provideType;

    /**
     * 任务详情页
     */
    @Column(name = "url")
    private String url;

    /**
     * 任务授权页
     */
    @Column(name = "connect_url")
    private String connectUrl;

    @Column(name = "connect_url_pc")
    private String connectUrlPc;

    /**
     * 奖品
     */
    @Column(name = "reward", isDefined = true)
    private String reward;

    @Column(name = "order", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "order", fieldType = FieldType.LONG)
    private Integer order;

    @Column(name = "filter", isDefined = true)
    private String filter;

    @Column(name = "domain", isDefined = true)
    private String domain;

    @Column(name = "attr")
    private String attr;

    /**
     * 允许的最低vip等级
     * 0：注册用户
     * 1：1级会员
     * 0+：大于等于注册用户
     * 1+：大于等于1级会员
     */
    @Column(name = "vip_level")
    private String vipLevel;

    @Column(name = "btn")
    private Integer btn;

    @Column(name = "link")
    private String link;

    @Column(name = "showProgress", type = Column.Type.BOOLEAN)
    private Boolean showProgress;

    @Column(name = "channel", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "channel", fieldType = FieldType.TEXT,
            analyzer = FieldSchema.Analyzer.Split, delimiter = ",")
    private String channel;

    @Column(name = "position", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "position")
    private String position;

    @Column(name = "ledger_title")
    private String ledgerTitle;

    @Column(name = "call_register", type = Column.Type.BOOLEAN)
    private Boolean callRegister;

    @Column(name = "last_post", type = Column.Type.BOOLEAN)
    private Boolean lastPost;

    @Column(name = "client_type", type = Column.Type.STRING)
    private String clientType;

    @Column(name = "skip_verification", type = Column.Type.BOOLEAN)
    private Boolean skipVerification;

    @Column(name = "type", type = Column.Type.STRING)
    private String type;

    @Column(name = "check_reward", type = Column.Type.STRING)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "check_reward")
    private String checkReward;
    @Column(name = "postTaskId", type = Column.Type.STRING)
    private String postTaskId;

    @Column(name = "postTaskCode", type = Column.Type.STRING)
    private String postTaskCode;

    @Column(name = "postTaskDesc", type = Column.Type.STRING)
    private String postTaskDesc;

    @Column(name = "postTaskReward", type = Column.Type.INTEGER)
    private Integer postTaskReward;

    @Column(name = "hiddenTaskCompleted", type = Column.Type.BOOLEAN)
    //任务完成后是否显示任务
    private Boolean hiddenTaskCompleted;

    @Column(name = "onlyVerifySocial", type = Column.Type.BOOLEAN)
    //任务完成后是否显示任务
    private Boolean onlyVerifySocial;

    public Map<String, String> getImageMap(){
        if(StringUtils.isBlank(this.image) && JSON.isValidObject(this.image)) {
            return new HashMap<>();
        }
        return JSON.parseObject(this.image, new TypeReference<Map<String, String>>(){});
    }

    public GroupAward getRewardGroup(){
        if(StringUtils.isBlank(this.reward) && JSON.isValidObject(this.image)){
            return null;
        }
        return JSON.parseObject(this.reward, GroupAward.class);
    }

    public Map<String, String> getAttrMap(){
        if(StringUtils.isBlank(this.attr) && JSON.isValidObject(this.image)){
            return new HashMap<>();
        }
        return JSON.parseObject(this.attr, new TypeReference<Map<String, String>>(){});
    }

    public Map<String, String> getLinkMap(){
        if(this.link == null || !JSON.isValidObject(this.image)){
            return new HashMap<>();
        }
        return JSON.parseObject(this.link, new TypeReference<Map<String, String>>(){});
    }
}
