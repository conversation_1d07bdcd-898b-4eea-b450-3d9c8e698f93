package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.kikitrade.activity.dal.tablestore.builder.UserClaimEntitlementBuilder;
import com.kikitrade.activity.dal.tablestore.model.UserClaimEntitlement;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户领奖凭证表数据访问层实现
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class UserClaimEntitlementBuilderImpl extends WideColumnStoreBuilder<UserClaimEntitlement> implements UserClaimEntitlementBuilder {

    @Override
    public String getTableName() {
        return "user_claim_entitlement";
    }
    @PostConstruct
    public void init() {
        super.init(UserClaimEntitlement.class);
    }

    @Override
    public boolean save(UserClaimEntitlement entitlement) {
        try {
            entitlement.setUpdateTime(System.currentTimeMillis());
            if (entitlement.getCreateTime() == null) {
                entitlement.setCreateTime(System.currentTimeMillis());
            }
            return super.putRow(entitlement);
        } catch (Exception e) {
            log.error("保存用户领奖凭证失败: userId={}, claimId={}", 
                     entitlement.getUserId(), entitlement.getClaimId(), e);
            return false;
        }
    }

    @Override
    public boolean insert(UserClaimEntitlement entitlement) {
        try {
            entitlement.setCreateTime(System.currentTimeMillis());
            entitlement.setUpdateTime(System.currentTimeMillis());
            return super.putRow(entitlement, RowExistenceExpectation.EXPECT_NOT_EXIST);
        } catch (Exception e) {
            log.error("插入用户领奖凭证失败: userId={}, claimId={}", 
                     entitlement.getUserId(), entitlement.getClaimId(), e);
            return false;
        }
    }

    @Override
    public boolean update(UserClaimEntitlement entitlement) {
        try {
            entitlement.setUpdateTime(System.currentTimeMillis());
            Condition condition = new Condition();
            condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
            return super.updateRow(entitlement, condition);
        } catch (Exception e) {
            log.error("更新用户领奖凭证失败: userId={}, claimId={}", 
                     entitlement.getUserId(), entitlement.getClaimId(), e);
            return false;
        }
    }

    @Override
    public UserClaimEntitlement findByUserIdAndClaimId(String userId, String claimId) {
        try {
            // 使用复合主键直接查询，性能最优
            UserClaimEntitlement entitlement = new UserClaimEntitlement();
            entitlement.setUserId(userId);
            entitlement.setClaimId(claimId);
            return super.getRow(entitlement);
        } catch (Exception e) {
            log.error("根据用户ID和凭证ID查询凭证失败: userId={}, claimId={}", userId, claimId, e);
            return null;
        }
    }

    @Override
    public List<UserClaimEntitlement> findUnclaimedByUserId(String userId) {
        try {
            BoolQuery.Builder builder = QueryBuilders.bool()
                    .must(QueryBuilders.term("user_id", userId))
                    .must(QueryBuilders.term("status", ActivityConstant.EntitlementStatusEnum.UNCLAIMED.name()));
            
            return pageSearchQuery(builder.build(), null, 0, 100, UserClaimEntitlement.SEARCH_USER_CLAIM_ENTITLEMENT).getRows();
        } catch (Exception e) {
            log.error("查询用户未领取凭证失败: userId={}", userId, e);
            return List.of();
        }
    }

    @Override
    public UserClaimEntitlement findBySourceTransactionId(String sourceTransactionId, String saasId) {
        try {
            BoolQuery.Builder builder = QueryBuilders.bool()
                    .must(QueryBuilders.term("source_transaction_id", sourceTransactionId))
                    .must(QueryBuilders.term("saas_id", saasId));
            
            return searchOne(builder.build(), UserClaimEntitlement.SEARCH_USER_CLAIM_ENTITLEMENT);
        } catch (Exception e) {
            log.error("根据来源交易ID查询凭证失败: sourceTransactionId={}, saasId={}", sourceTransactionId, saasId, e);
            return null;
        }
    }

    @Override
    public List<UserClaimEntitlement> findExpiredEntitlements(Long currentTime, String saasId) {
        try {
            BoolQuery.Builder builder = QueryBuilders.bool()
                    .must(QueryBuilders.term("status", "UNCLAIMED"))
                    .must(QueryBuilders.term("saas_id", saasId))
                    .must(QueryBuilders.bool().filter(QueryBuilders.range("expire_time").lessThan(currentTime)));

            return pageSearchQuery(builder.build(), null, 0, 1000, UserClaimEntitlement.SEARCH_USER_CLAIM_ENTITLEMENT).getRows();
        } catch (Exception e) {
            log.error("查询过期凭证失败: currentTime={}, saasId={}", currentTime, saasId, e);
            return List.of();
        }
    }

    @Override
    public List<UserClaimEntitlement> findByUserId(String userId) {
        try {
            BoolQuery.Builder builder = QueryBuilders.bool()
                    .must(QueryBuilders.term("user_id", userId));

            return pageSearchQuery(builder.build(), null, 0, 1000, UserClaimEntitlement.SEARCH_USER_CLAIM_ENTITLEMENT).getRows();
        } catch (Exception e) {
            log.error("根据用户ID查询凭证失败: userId={}", userId, e);
            return List.of();
        }
    }

    @Override
    public List<UserClaimEntitlement> findByUserIdAndRewardType(String userId, String rewardType) {
        try {
            BoolQuery.Builder builder = QueryBuilders.bool()
                    .must(QueryBuilders.term("user_id", userId))
                    .must(QueryBuilders.term("reward_type", rewardType));

            return pageSearchQuery(builder.build(), null, 0, 100, UserClaimEntitlement.SEARCH_USER_CLAIM_ENTITLEMENT).getRows();
        } catch (Exception e) {
            log.error("根据用户ID和奖励类型查询凭证失败: userId={}, rewardType={}", userId, rewardType, e);
            return List.of();
        }
    }

    @Override
    public List<UserClaimEntitlement> findByUserIdAndStatus(String userId, String status) {
        try {
            BoolQuery.Builder builder = QueryBuilders.bool()
                    .must(QueryBuilders.term("user_id", userId))
                    .must(QueryBuilders.term("status", status));

            return pageSearchQuery(builder.build(), null, 0, 100, UserClaimEntitlement.SEARCH_USER_CLAIM_ENTITLEMENT).getRows();
        } catch (Exception e) {
            log.error("根据用户ID和状态查询凭证失败: userId={}, status={}", userId, status, e);
            return List.of();
        }
    }

    @Override
    public boolean delete(String userId, String claimId) {
        try {
            UserClaimEntitlement entitlement = new UserClaimEntitlement();
            entitlement.setUserId(userId);
            entitlement.setClaimId(claimId);
            return super.deleteRow(entitlement);
        } catch (Exception e) {
            log.error("删除凭证失败: userId={}, claimId={}", userId, claimId, e);
            return false;
        }
    }

    @Override
    public UserClaimEntitlement findUnClaimedChest(String userId, String chestId, Long cycleStartTime) {
        try {
            BoolQuery.Builder builder = QueryBuilders.bool()
                    .must(QueryBuilders.term("user_id", userId))
                    .must(QueryBuilders.term("reward_type", ActivityConstant.RewardTypeEnum.PROGRESS_CHEST.name()))
                    .must(QueryBuilders.term("reward_source_id", chestId))
                    .must(QueryBuilders.term("status", "UNCLAIMED"))
                    .must(QueryBuilders.bool().filter(QueryBuilders.range("createTime").greaterThanOrEqual(cycleStartTime)));
            return searchOne(builder.build(), UserClaimEntitlement.SEARCH_USER_CLAIM_ENTITLEMENT);
        } catch (Exception e) {
            log.error("根据用户ID和宝箱ID查询未领取的凭证失败: userId={}, chestId={}", userId, chestId, e);
            return null;
        }
    }

}
