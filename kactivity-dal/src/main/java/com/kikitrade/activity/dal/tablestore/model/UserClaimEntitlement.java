package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户领奖凭证表数据模型
 * 对应技术规格书中的 user_claim_entitlement 表
 * 统一管理所有类型的奖励领取凭证
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Table(name = "user_claim_entitlement")
public class UserClaimEntitlement implements Serializable {

    public static final String SEARCH_USER_CLAIM_ENTITLEMENT = "search_user_claim_entitlement";

    /**
     * 用户ID（分区键）
     */
    @PartitionKey(name = "user_id")
    @SearchIndex(name = SEARCH_USER_CLAIM_ENTITLEMENT, column = "user_id")
    private String userId;

    /**
     * 领奖凭证唯一ID（排序键）
     */
    @PartitionKey(name = "claim_id", value = 1)
    @SearchIndex(name = SEARCH_USER_CLAIM_ENTITLEMENT, column = "claim_id")
    private String claimId;

    /**
     * 场景编码 (用于前端展示定位)
     */
    @Column(name = "scene_code")
    @SearchIndex(name = SEARCH_USER_CLAIM_ENTITLEMENT, column = "scene_code")
    private String sceneCode;

    /**
     * 奖励类型
     * PROGRESS_CHEST: 进度宝箱
     * GRANTED_PACK: 授予的礼包
     */
    @Column(name = "reward_type")
    @SearchIndex(name = SEARCH_USER_CLAIM_ENTITLEMENT, column = "reward_type")
    private String rewardType;

    /**
     * 奖励来源ID (如 chestId 或 packId)
     */
    @Column(name = "reward_source_id")
    @SearchIndex(name = SEARCH_USER_CLAIM_ENTITLEMENT, column = "reward_source_id")
    private String rewardSourceId;

    /**
     * 奖励名称（冗余字段，便于显示）
     */
    @Column(name = "reward_name")
    private String rewardName;

    /**
     * 奖励图标URL（冗余字段，便于显示）
     */
    @Column(name = "reward_icon")
    private String rewardIcon;

    /**
     * 奖励描述
     */
    @Column(name = "reward_description")
    private String rewardDescription;

    /**
     * 状态
     * UNCLAIMED: 未领取
     * CLAIMED: 已领取
     * EXPIRED: 已过期
     */
    @Column(name = "status")
    @SearchIndex(name = SEARCH_USER_CLAIM_ENTITLEMENT, column = "status")
    private String status;

    /**
     * 凭证来源渠道
     * SYSTEM_AUTO: 系统自动生成
     * TASK_SYSTEM: 任务系统
     * LEADERBOARD: 排行榜系统
     * MANUAL: 手动发放
     */
    @Column(name = "source_channel")
    private String sourceChannel;

    /**
     * 来源渠道的唯一交易ID (幂等键)
     */
    @Column(name = "source_transaction_id")
    @SearchIndex(name = SEARCH_USER_CLAIM_ENTITLEMENT, column = "source_transaction_id")
    private String sourceTransactionId;

    /**
     * 过期时间
     */
    @Column(name = "expire_time", type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_USER_CLAIM_ENTITLEMENT, column = "expire_time")
    private Long expireTime;

    /**
     * 领取时间
     */
    @Column(name = "claim_time", type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_USER_CLAIM_ENTITLEMENT, column = "claim_time")
    private Long claimTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_USER_CLAIM_ENTITLEMENT, column = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = Column.Type.INTEGER)
    private Long updateTime;

    /**
     * SaaS ID
     */
    @Column(name = "saas_id")
    @SearchIndex(name = SEARCH_USER_CLAIM_ENTITLEMENT, column = "saas_id")
    private String saasId;

    /**
     * 扩展属性（JSON格式）
     */
    @Column(name = "extra_properties")
    private String extraProperties;
}
