package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;

import java.io.Serializable;

/**
 * 进度宝箱配置表数据模型
 * 对应技术规格书中的 progress_chest_config 表
 * 存储进度宝箱的解锁条件、奖品内容等配置
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Table(name = "progress_chest_config")
public class ProgressChestConfig implements Serializable {

    public static final String SEARCH_PROGRESS_CHEST_CONFIG = "search_progress_chest_config";
    public static final String INDEX_PROGRESS_CHEST_CONFIG = "index_progress_chest_config";

    /**
     * SaaS ID（分区键）
     */
    @PartitionKey(name = "saas_id")
    @SearchIndex(name = SEARCH_PROGRESS_CHEST_CONFIG, column = "saas_id")
    @Index(name = INDEX_PROGRESS_CHEST_CONFIG, pkColumn = "saas_id")
    private String saasId;

    /**
     * 关联的奖池编码（排序键1）
     */
    @PartitionKey(name = "prize_pool_code", value = 1)
    @SearchIndex(name = SEARCH_PROGRESS_CHEST_CONFIG, column = "prize_pool_code")
    @Index(name = INDEX_PROGRESS_CHEST_CONFIG, pkColumn = "prize_pool_code", pkValue = 1)
    private String prizePoolCode;

    /**
     * 宝箱配置的唯一ID（排序键2）
     */
    @PartitionKey(name = "chest_id", value = 2)
    @SearchIndex(name = SEARCH_PROGRESS_CHEST_CONFIG, column = "chest_id")
    @Index(name = INDEX_PROGRESS_CHEST_CONFIG, pkColumn = "chest_id", pkValue = 2)
    private String chestId;

    /**
     * 关联的偏好类型 (NULL表示通用)
     */
    @Column(name = "preference_type")
    @SearchIndex(name = SEARCH_PROGRESS_CHEST_CONFIG, column = "preference_type")
    private String preferenceType;

    /**
     * 关联的偏好值
     */
    @Column(name = "preference_value")
    @SearchIndex(name = SEARCH_PROGRESS_CHEST_CONFIG, column = "preference_value")
    private String preferenceValue;

    /**
     * 场景编码 (用于前端展示定位)
     */
    @Column(name = "scene_code")
    @SearchIndex(name = SEARCH_PROGRESS_CHEST_CONFIG, column = "scene_code")
    private String sceneCode;

    /**
     * 宝箱名称
     */
    @Column(name = "chest_name")
    private String chestName;

    /**
     * 解锁所需的进度值
     */
    @Column(name = "unlock_progress", type = Column.Type.INTEGER)
    private Integer unlockProgress;

    /**
     * 解锁时对应的礼包ID
     */
    @Column(name = "pack_id_on_unlock")
    private String packIdOnUnlock;

    /**
     * 显示顺序
     */
    @Column(name = "display_order", type = Column.Type.INTEGER)
    private Long displayOrder;

    /**
     * 是否启用
     */
    @Column(name = "is_active", type = Column.Type.BOOLEAN)
    @SearchIndex(name = SEARCH_PROGRESS_CHEST_CONFIG, column = "is_active")
    private Boolean isActive;

    /**
     * 宝箱描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 宝箱图标URL
     */
    @Column(name = "icon_url")
    private String iconUrl;

    /**
     * 进度类型
     * LOGIN_DAYS: 登录天数
     * DRAW_COUNT: 抽奖次数
     * WIN_COUNT: 中奖次数
     * POINTS_EARNED: 获得积分
     * LEVEL_REACHED: 达到等级
     * TASKS_COMPLETED: 完成任务数
     */
    @Column(name = "progress_type")
    private String progressType;

    /**
     * 解锁条件（JSON格式）
     * 存储额外的解锁条件
     */
    @Column(name = "unlock_conditions")
    private String unlockConditions;

    /**
     * 有效期开始时间（时间戳）
     */
    @Column(name = "valid_start_time", type = Column.Type.INTEGER)
    private Long validStartTime;

    /**
     * 有效期结束时间（时间戳）
     */
    @Column(name = "valid_end_time", type = Column.Type.INTEGER)
    private Long validEndTime;

    /**
     * 宝箱价值（用于排序和展示）
     */
    @Column(name = "chest_value", type = Column.Type.INTEGER)
    private Integer chestValue;

    /**
     * 每用户最大领取次数（-1表示无限制）
     */
    @Column(name = "max_claim_per_user", type = Column.Type.INTEGER)
    private Integer maxClaimPerUser;

    /**
     * 重置周期（天）
     * 0: 不重置
     * 1: 每日重置
     * 7: 每周重置
     * 30: 每月重置
     */
    @Column(name = "reset_cycle_days", type = Column.Type.INTEGER)
    private Integer resetCycleDays;

    /**
     * 宝箱优先级（数字越小优先级越高）
     */
    @Column(name = "priority", type = Column.Type.INTEGER)
    private Integer priority;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = Column.Type.INTEGER)
    private Long updateTime;

    /**
     * 创建者
     */
    @Column(name = "creator")
    private String creator;

    /**
     * 扩展属性（JSON格式）
     * 存储其他自定义属性
     */
    @Column(name = "extra_properties")
    private String extraProperties;
}
