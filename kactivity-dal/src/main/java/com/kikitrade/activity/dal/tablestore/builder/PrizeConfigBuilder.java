package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.PrizeConfig;

import java.util.List;

/**
 * 奖品配置表数据访问层接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface PrizeConfigBuilder {

    String getTableName();

    /**
     * 根据奖池编码查询所有活跃的奖品配置
     */
    List<PrizeConfig> findActiveByPrizePoolCode(String saasId, String prizePoolCode);

    /**
     * 根据奖池编码和偏好类型、偏好值查询奖品配置
     */
    List<PrizeConfig> findByPrizePoolCodeAndPreference(String saasId, String prizePoolCode, String preferenceType, String preferenceValue);

    /**
     * 根据奖池编码查询通用奖品（不绑定偏好）
     */
    List<PrizeConfig> findCommonPrizesByPoolCode(String saasId, String prizePoolCode);

    /**
     * 根据奖品类型查询
     */
    List<PrizeConfig> findByPrizeType(String prizePoolCode, String prizeType);

    /**
     * 插入奖品配置
     */
    boolean insert(PrizeConfig prizeConfig);

    /**
     * 更新奖品配置
     */
    boolean update(PrizeConfig prizeConfig);

    /**
     * 根据ID查询奖品配置
     */
    PrizeConfig findById(String saasId, String prizePoolCode, String prizeId);
}