package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.ProgressChestConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/9/3 14:31
 * @description:
 */
public interface ProgressChestConfigBuilder {

    String getTableName();

    boolean insert(ProgressChestConfig progressChestConfig);

    boolean update(ProgressChestConfig progressChestConfig);

    List<ProgressChestConfig> findBySaasIdAndPrizePoolCode(String saasId, String prizePoolCode);

    ProgressChestConfig findByChestId(String chestId, String saasId);

}
