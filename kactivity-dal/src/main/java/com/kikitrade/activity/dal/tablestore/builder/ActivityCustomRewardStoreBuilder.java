package com.kikitrade.activity.dal.tablestore.builder;

import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardParam;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.domain.RewardRequest;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.ots.RangeResult;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ActivityCustomRewardStoreBuilder {

    ActivityCustomReward findByPrimaryId(String batchId, String customerId, String seq);

    boolean insert(ActivityCustomReward reward);

    boolean updateStatus(ActivityCustomReward activityCustomReward);

    ActivityCustomReward findOne(RewardRequest rewardRequest);

    ActivityCustomReward findByBusinessId(String customerId, String businessId);
}
