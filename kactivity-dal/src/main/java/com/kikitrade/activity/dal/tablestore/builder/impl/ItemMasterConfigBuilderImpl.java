package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.kikitrade.activity.dal.tablestore.builder.ItemMasterConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.ItemMasterConfig;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.kikitrade.activity.dal.tablestore.model.ItemMasterConfig.SEARCH_ITEM_MASTER_CONFIG;

/**
 * 物品主数据表数据访问层实现
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class ItemMasterConfigBuilderImpl extends WideColumnStoreBuilder<ItemMasterConfig> implements ItemMasterConfigBuilder {

    @Override
    public String getTableName() {
        return "item_master_config";
    }

    @PostConstruct
    public void init() {
        super.init(ItemMasterConfig.class);
    }

    @Override
    public ItemMasterConfig findByItemId(String saasId, String itemId) {
        try {
            ItemMasterConfig itemMasterConfig = new ItemMasterConfig();
            itemMasterConfig.setSaasId(saasId);
            itemMasterConfig.setItemId(itemId);
            return getRow(itemMasterConfig);
        } catch (Exception e) {
            log.error("查询物品主数据失败, saasId: {}, itemId: {}", saasId, itemId, e);
            return null;
        }
    }

    @Override
    public List<ItemMasterConfig> findActiveByItemType(String saasId, String itemType) {
        BoolQuery.Builder builder = QueryBuilders.bool()
                .must(QueryBuilders.term("saas_id", saasId))
                .must(QueryBuilders.term("item_type", itemType))
                .must(QueryBuilders.term("is_active", true));
        
        return pageSearchQuery(builder.build(), null, 0, 100, SEARCH_ITEM_MASTER_CONFIG).getRows();
    }

    @Override
    public ItemMasterConfig findByExternalItemId(String saasId, String externalItemId) {
        BoolQuery.Builder builder = QueryBuilders.bool()
                .must(QueryBuilders.term("saas_id", saasId))
                .must(QueryBuilders.term("external_item_id", externalItemId));
        
        List<ItemMasterConfig> results = pageSearchQuery(builder.build(), null, 0, 1, SEARCH_ITEM_MASTER_CONFIG).getRows();
        return results.isEmpty() ? null : results.get(0);
    }

    @Override
    public List<ItemMasterConfig> findActiveBySaasId(String saasId) {
        BoolQuery.Builder builder = QueryBuilders.bool()
                .must(QueryBuilders.term("saas_id", saasId))
                .must(QueryBuilders.term("is_active", true));
        
        return pageSearchQuery(builder.build(), null, 0, 1000, SEARCH_ITEM_MASTER_CONFIG).getRows();
    }

    @Override
    public List<ItemMasterConfig> findByItemIds(String saasId, List<String> itemIds) {
        if (itemIds == null || itemIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<ItemMasterConfig> results = new ArrayList<>();
        for (String itemId : itemIds) {
            ItemMasterConfig item = findByItemId(saasId, itemId);
            if (item != null) {
                results.add(item);
            }
        }
        return results;
    }

    @Override
    public boolean insert(ItemMasterConfig itemMasterConfig) {
        try {
            itemMasterConfig.setCreateTime(System.currentTimeMillis());
            return putRow(itemMasterConfig, RowExistenceExpectation.EXPECT_NOT_EXIST);
        } catch (Exception e) {
            log.error("插入物品主数据失败", e);
            return false;
        }
    }

    @Override
    public boolean update(ItemMasterConfig itemMasterConfig) {
        try {
            itemMasterConfig.setUpdateTime(System.currentTimeMillis());
            Condition condition = new Condition();
            condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
            return super.updateRow(itemMasterConfig, condition);
        } catch (Exception e) {
            log.error("更新物品主数据失败", e);
            return false;
        }
    }

    @Override
    public boolean deactivate(String saasId, String itemId) {
        try {
            ItemMasterConfig item = findByItemId(saasId, itemId);
            if (item != null) {
                item.setIsActive(false);
                item.setUpdateTime(System.currentTimeMillis());
                update(item);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("软删除物品主数据失败, saasId: {}, itemId: {}", saasId, itemId, e);
            return false;
        }
    }
}
