package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alibaba.fastjson.JSON;
import com.alicloud.openservices.tablestore.model.*;
import com.alicloud.openservices.tablestore.model.filter.CompositeColumnValueFilter;
import com.alicloud.openservices.tablestore.model.filter.SingleColumnValueFilter;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.SearchResponse;
import com.alicloud.openservices.tablestore.model.search.agg.AggregationBuilders;
import com.alicloud.openservices.tablestore.model.search.groupby.GroupByBuilders;
import com.alicloud.openservices.tablestore.model.search.groupby.GroupByFieldResultItem;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.google.common.collect.Lists;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardParam;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.domain.RewardRequest;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.ots.RangeResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward.*;

@Service
@Slf4j
public class ActivityCustomRewardStoreBuilderImpl extends WideColumnStoreBuilder<ActivityCustomReward> implements ActivityCustomRewardStoreBuilder {

    @PostConstruct
    public void init() {
        init(ActivityCustomReward.class);
    }

    @Override
    public ActivityCustomReward findByPrimaryId(String batchId, String customerId, String seq) {
        ActivityCustomReward reward = new ActivityCustomReward();
        reward.setBatchId(batchId);
        reward.setCustomerId(customerId);
        reward.setSeq(seq);
        return super.getRow(reward);
    }

    @Override
    public boolean insert(ActivityCustomReward reward) {
        return putRow(reward, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean updateStatus(ActivityCustomReward activityCustomReward) {
        activityCustomReward.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return updateRow(activityCustomReward);
    }

    @Override
    public ActivityCustomReward findOne(RewardRequest rewardRequest) {
        BoolQuery boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("customer_id", rewardRequest.getCustomerId()))
                .must(QueryBuilders.term("business_type", rewardRequest.getBusinessType()))
                .must(QueryBuilders.term("batch_id", rewardRequest.getBatchId()))
                .must(QueryBuilders.term("status", ActivityConstant.RewardStatusEnum.AWARDABLE.name()))
                .build();
        return searchOne(boolQuery, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX);
    }

    @Override
    public ActivityCustomReward findByBusinessId(String customerId, String businessId){
        BoolQuery boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("customer_id", customerId))
                .must(QueryBuilders.term("business_id", businessId))
                .build();
        return searchOne(boolQuery, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX);
    }
}
