package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.kikitrade.activity.dal.tablestore.builder.UserPreferenceBuilder;
import com.kikitrade.activity.dal.tablestore.model.UserPreference;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户偏好表数据访问层实现
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class UserPreferenceBuilderImpl extends WideColumnStoreBuilder<UserPreference> implements UserPreferenceBuilder {

    @Override
    public String getTableName() {
        return "user_preference";
    }

    @PostConstruct
    public void init() {
        super.init(UserPreference.class);
    }

    @Override
    public boolean insert(UserPreference preference) {
        try {
            preference.setCreateTime(System.currentTimeMillis());
            preference.setUpdateTime(System.currentTimeMillis());
            if (preference.getIsEnabled() == null) {
                preference.setIsEnabled(true);
            }
            return super.putRow(preference, RowExistenceExpectation.EXPECT_NOT_EXIST);
        } catch (Exception e) {
            log.error("插入用户偏好失败: userId={}, prizePoolCode={}, preferenceType={}", 
                     preference.getUserId(), preference.getPrizePoolCode(), preference.getPreferenceType(), e);
            return false;
        }
    }

    @Override
    public boolean update(UserPreference preference) {
        try {
            preference.setUpdateTime(System.currentTimeMillis());
            Condition condition = new Condition();
            condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
            return super.updateRow(preference, condition);
        } catch (Exception e) {
            log.error("更新用户偏好失败: userId={}, prizePoolCode={}, preferenceType={}", 
                     preference.getUserId(), preference.getPrizePoolCode(), preference.getPreferenceType(), e);
            return false;
        }
    }

    @Override
    public boolean delete(String userId, String prizePoolCode, String preferenceType) {
        try {
            UserPreference preference = new UserPreference();
            preference.setUserId(userId);
            preference.setPrizePoolCode(prizePoolCode);
            preference.setPreferenceType(preferenceType);
            return super.deleteRow(preference);
        } catch (Exception e) {
            log.error("删除用户偏好失败: userId={}, prizePoolCode={}, preferenceType={}", 
                     userId, prizePoolCode, preferenceType, e);
            return false;
        }
    }

    @Override
    public List<UserPreference> findByUserIdAndPrizePoolCode(String userId, String prizePoolCode) {
        try {
            BoolQuery.Builder builder = QueryBuilders.bool()
                    .must(QueryBuilders.term("user_id", userId))
                    .must(QueryBuilders.term("prize_pool_code", prizePoolCode))
                    .must(QueryBuilders.term("is_enabled", true));
            
            return pageSearchQuery(builder.build(), null, 0, 100, UserPreference.SEARCH_USER_PREFERENCE).getRows();
        } catch (Exception e) {
            log.error("查询用户偏好失败: userId={}, prizePoolCode={}", userId, prizePoolCode, e);
            return List.of();
        }
    }

    @Override
    public UserPreference findByUserIdAndPrizePoolCodeAndType(String userId, String prizePoolCode, String preferenceType) {
        try {
            UserPreference preference = new UserPreference();
            preference.setUserId(userId);
            preference.setPrizePoolCode(prizePoolCode);
            preference.setPreferenceType(preferenceType);
            return super.getRow(preference);
        } catch (Exception e) {
            log.error("查询用户偏好失败: userId={}, prizePoolCode={}, preferenceType={}", 
                     userId, prizePoolCode, preferenceType, e);
            return null;
        }
    }

    @Override
    public List<UserPreference> findByUserId(String userId) {
        try {
            BoolQuery.Builder builder = QueryBuilders.bool()
                    .must(QueryBuilders.term("user_id", userId))
                    .must(QueryBuilders.term("is_enabled", true));
            
            return pageSearchQuery(builder.build(), null, 0, 1000, UserPreference.SEARCH_USER_PREFERENCE).getRows();
        } catch (Exception e) {
            log.error("查询用户所有偏好失败: userId={}", userId, e);
            return List.of();
        }
    }

    @Override
    public Map<String, String> getUserPreferenceMap(String userId, String prizePoolCode) {
        try {
            List<UserPreference> preferences = findByUserIdAndPrizePoolCode(userId, prizePoolCode);
            return preferences.stream()
                    .filter(p -> p.getIsEnabled() != null && p.getIsEnabled())
                    .collect(Collectors.toMap(
                            UserPreference::getPreferenceType,
                            UserPreference::getPreferenceValue,
                            (existing, replacement) -> replacement // 如果有重复key，使用新值
                    ));
        } catch (Exception e) {
            log.error("获取用户偏好Map失败: userId={}, prizePoolCode={}", userId, prizePoolCode, e);
            return new HashMap<>();
        }
    }

    @Override
    public boolean batchSetPreferences(String userId, String prizePoolCode, Map<String, String> preferences) {
        try {
            boolean allSuccess = true;
            
            for (Map.Entry<String, String> entry : preferences.entrySet()) {
                String preferenceType = entry.getKey();
                String preferenceValue = entry.getValue();
                
                // 检查是否已存在
                UserPreference existing = findByUserIdAndPrizePoolCodeAndType(userId, prizePoolCode, preferenceType);
                
                UserPreference preference = new UserPreference();
                preference.setUserId(userId);
                preference.setPrizePoolCode(prizePoolCode);
                preference.setPreferenceType(preferenceType);
                preference.setPreferenceValue(preferenceValue);
                preference.setSource("USER_MANUAL");
                preference.setIsEnabled(true);
                
                boolean success;
                if (existing != null) {
                    success = update(preference);
                } else {
                    success = insert(preference);
                }
                
                if (!success) {
                    allSuccess = false;
                    log.warn("设置用户偏好失败: userId={}, prizePoolCode={}, preferenceType={}", 
                            userId, prizePoolCode, preferenceType);
                }
            }
            
            return allSuccess;
        } catch (Exception e) {
            log.error("批量设置用户偏好失败: userId={}, prizePoolCode={}", userId, prizePoolCode, e);
            return false;
        }
    }
}
