package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.kikitrade.activity.dal.tablestore.builder.RandomRewardPoolBuilder;
import com.kikitrade.activity.dal.tablestore.model.RandomRewardPool;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 随机奖励池表数据访问层实现
 * 优化：适配新的三元主键 (saas_id, pool_id, item_id) 结构
 * 
 * <AUTHOR>
 * @date 2025/9/4 10:48
 */
@Slf4j
@Component
public class RandomRewardPoolBuilderImpl extends WideColumnStoreBuilder<RandomRewardPool> implements RandomRewardPoolBuilder {
    @Override
    public String getTableName() {
        return "random_reward_pool";
    }
    @PostConstruct
    public void init() {
        super.init(RandomRewardPool.class);
    }
    /**
     * 根据随机池ID查询奖励池配置
     * 使用范围查询，以saas_id为分区键
     *
     * @param poolId 随机池ID
     * @param saasId SaaS ID
     * @return 奖励池配置列表
     */
    @Override
    public List<RandomRewardPool> findByPoolId(String poolId, String saasId) {
        try {
            List<RangeQueryParameter> parameters = new ArrayList<>();
            parameters.add(new RangeQueryParameter("saas_id", PrimaryKeyValue.fromString(saasId)));
            parameters.add(new RangeQueryParameter("pool_id", PrimaryKeyValue.fromString(poolId)));
            parameters.add(new RangeQueryParameter("item_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
            List<RandomRewardPool> randomRewardPools = rangeQuery(parameters);

            // 过滤活跃的记录
            return randomRewardPools.stream()
                    .filter(item -> Boolean.TRUE.equals(item.getIsActive()))
                    .toList();
        } catch (Exception e) {
            log.error("根据随机池ID查询奖励池配置失败: poolId={}, saasId={}", poolId, saasId, e);
            return List.of();
        }
    }

    /**
     * 根据三元主键查询单个奖励池配置
     *
     * @param saasId SaaS ID
     * @param poolId 随机池ID
     * @param itemId 物品ID
     * @return 奖励池配置
     */
    @Override
    public RandomRewardPool findBySaasIdAndPoolIdAndItemId(String saasId, String poolId, String itemId) {
        try {
            RandomRewardPool randomRewardPool = new RandomRewardPool();
            randomRewardPool.setSaasId(saasId);
            randomRewardPool.setPoolId(poolId);
            randomRewardPool.setItemId(itemId);

            return getRow(randomRewardPool);
        } catch (Exception e) {
            log.error("根据三元主键查询奖励池配置失败: saasId={}, poolId={}, itemId={}", saasId, poolId, itemId, e);
            return null;
        }
    }

    /**
     * 根据随机池ID和物品类型查询奖励池配置
     * 需要结合ItemMasterConfig查询物品类型（此方法应在业务层实现）
     *
     * @param poolId   随机池ID
     * @param itemType 物品类型
     * @param saasId   SaaS ID
     * @return 奖励池配置列表
     */
    @Override
    public List<RandomRewardPool> findByPoolIdAndItemType(String poolId, String itemType, String saasId) {
        // 由于移除了凗余字段，该方法需要在业务层实现
        // 首先获取所有的池配置，然后在业务层通过ItemMasterConfig过滤
        log.warn("该方法已废弃，请在业务层使用 findByPoolId + ItemMasterConfig 过滤");
        return findByPoolId(poolId, saasId);
    }


    @Override
    public boolean insert(RandomRewardPool randomRewardPool) {
        try {
            randomRewardPool.setCreateTime(System.currentTimeMillis());
            return putRow(randomRewardPool, RowExistenceExpectation.EXPECT_NOT_EXIST);
        } catch (Exception e) {
            log.error("插入奖励池配置失败", e);
            return false;
        }
    }

    @Override
    public boolean update(RandomRewardPool randomRewardPool) {
        try {
            randomRewardPool.setUpdateTime(System.currentTimeMillis());
            Condition condition = new Condition();
            condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
            return super.updateRow(randomRewardPool, condition);
        } catch (Exception e) {
            log.error("更新奖励池配置失败", e);
            return false;
        }
    }

    @Override
    public boolean delete(String saasId, String poolId, String itemId) {
        try {
            RandomRewardPool randomRewardPool = new RandomRewardPool();
            randomRewardPool.setSaasId(saasId);
            randomRewardPool.setPoolId(poolId);
            randomRewardPool.setItemId(itemId);
            return super.deleteRow(randomRewardPool);
        } catch (Exception e) {
            log.error("删除奖励池配置失败: saasId={}, poolId={}, itemId={}", saasId, poolId, itemId, e);
            return false;
        }
    }
}
