package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.UserProgressTracker;

/**
 * <AUTHOR>
 * @date 2025/9/3 10:07
 * @description:
 */
public interface UserProgressTrackerBuilder {
    String getTableName();
    /**
     * 插入用户进度
     * @param userProgressTracker 用户进度
     * @return 是否成功
     */
    boolean insert(UserProgressTracker userProgressTracker);
    /**
     * 更新用户进度
     * @param userProgressTracker 用户进度
     * @return 是否成功
     */
    boolean update(UserProgressTracker userProgressTracker);
    /**
     * 根据用户ID和奖池编码查询用户进度
     * @param userId 用户id
     * @param prizePoolCode 奖池code
     * @return 用户进度
     */
    UserProgressTracker findByUserIdAndPrizePoolCode(String userId, String prizePoolCode);
}
