package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.RandomRewardPool;

import java.util.List;

/**
 * 随机奖励池表数据访问层
 * 优化：使用三元主键 (saas_id, pool_id, item_id)
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface RandomRewardPoolBuilder {

    String getTableName();

    /**
     * 根据随机池ID查询奖励池配置
     * 
     * @param poolId 随机池ID
     * @param saasId SaaS ID
     * @return 奖励池配置列表
     */
    List<RandomRewardPool> findByPoolId(String poolId, String saasId);

    /**
     * 根据三元主键查询单个奖励池配置
     * 
     * @param saasId SaaS ID
     * @param poolId 随机池ID
     * @param itemId 物品ID
     * @return 奖励池配置
     */
    RandomRewardPool findBySaasIdAndPoolIdAndItemId(String saasId, String poolId, String itemId);

    /**
     * 插入奖励池配置
     * 
     * @param randomRewardPool 奖励池配置
     * @return 是否成功
     */
    boolean insert(RandomRewardPool randomRewardPool);

    /**
     * 更新奖励池配置
     * 
     * @param randomRewardPool 奖励池配置
     * @return 是否成功
     */
    boolean update(RandomRewardPool randomRewardPool);

    /**
     * 根据三元主键删除奖励池配置
     * 
     * @param saasId SaaS ID
     * @param poolId 随机池ID
     * @param itemId 物品ID
     * @return 是否成功
     */
    boolean delete(String saasId, String poolId, String itemId);

    /**
     * 根据随机池ID和物品类型查询奖励池配置
     * 
     * @param poolId 随机池ID
     * @param itemType 物品类型
     * @param saasId SaaS ID
     * @return 奖励池配置列表
     */
    List<RandomRewardPool> findByPoolIdAndItemType(String poolId, String itemType, String saasId);
}
