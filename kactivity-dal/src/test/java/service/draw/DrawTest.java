package service.draw;

import com.alibaba.fastjson2.JSON;
import com.kikitrade.activity.dal.tablestore.builder.PrizePoolBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ProgressChestConfigBuilder;
import com.kikitrade.activity.dal.tablestore.builder.UserPreferenceBuilder;
import com.kikitrade.activity.dal.tablestore.model.PrizePool;
import com.kikitrade.activity.dal.tablestore.model.ProgressChestConfig;
import com.kikitrade.activity.dal.tablestore.model.UserPreference;
import jakarta.annotation.Resource;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/9/8 16:48
 * @description:
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = DrawTest.class)
@TestPropertySource(locations = "classpath:application-local.properties")
@SpringBootApplication(scanBasePackages = {"com.kikitrade.activity"})
public class DrawTest {


    @Resource
    private UserPreferenceBuilder userPreferenceBuilder;
    @Resource
    private ProgressChestConfigBuilder progressChestConfigBuilder;
    @Resource
    private PrizePoolBuilder prizePoolBuilder;

    @Test
    public void testDraw() {
        System.out.println("=== 抽奖测试 ===");
    }

    @Test
    public void testUserPreference() {
        System.out.println("=== 获取用户抽奖状态测试 ===");
        List<UserPreference> obt2HeroWheel = userPreferenceBuilder.findByUserIdAndPrizePoolCode("", "OBT2_HERO_WHEEL");
        System.out.println(obt2HeroWheel);

    }

    @Test
    public void testProgressChestConfig() {
        List<ProgressChestConfig> bySaasIdAndPrizePoolCode =
            progressChestConfigBuilder.findBySaasIdAndPrizePoolCode("mugen", "OBT2_HERO_WHEEL");
        System.out.println(bySaasIdAndPrizePoolCode);

    }

    @Test
    public void testPrizePool() {
        PrizePool prizePool = prizePoolBuilder.findByCodeAndSaasId("OBT2_HERO_WHEEL", "mugen");
        List<ExchangeRule> exchangeRulesList =
            JSON.parseArray(prizePool.getExchangeRules(), ExchangeRule.class);

        for (ExchangeRule rule : exchangeRulesList) {            // 构建兑换选项

            // 解析选项数据 - 假设在rule中有options字段
            List<ExchangeOption> options = rule.getOptions();
            options.forEach(option -> {
                System.out.println(JSON.toJSONString(option));
            });

        }
    }

    @Data
    public class ExchangeRule {
        private String exchangeType;
        private String assetType;
        private List<ExchangeOption> options;
    }

    @Data
    public class ExchangeOption {
        private String optionId;
        private Integer cost;
        private Integer tickets;
        private String description;
    }
}
