saas-id=mugen
logging.pattern.console=[%p][%t][%d{yyyy-MM-dd HH:mm:ss.SSS}][%c][%L][%X{traceId}][%X{spanId}][%X{customerId}]%m%n

###########################zookeeper###################################
#zookeeper.url=zookeeper.quests-dev.svc.kiki.local:2181

############### dubbo ?? ###############
dubbo.reference.check=false
dubbo.consumer.check=false
dubbo.registry.check=false
dubbo.application.name=kactivity
dubbo.name=kactivity
dubbo.application.id=kactivity
dubbo.application.version=1.0.0
#dubbo.protocol.server=netty
dubbo.protocol.name=dubbo
dubbo.protocol.port=20880
dubbo.protocol.threadpool=fixed
dubbo.protocol.threads=50
dubbo.protocol.queues=1000
dubbo.provider.timeout=10000
dubbo.consumer.timeout=10000
dubbo.registry.address=zookeeper://127.0.0.1:2181
dubbo.consumer.group=kktd
dubbo.provider.group=kktd
dubbo.application.parameters.router=traffic
dubbo.provider.parameters.traffic=${TRAFFIC_TAG}
dubbo.provider.prefer-serialization=hessian2,fastjson2
dubbo.provider.serialization=hessian2
dubbo.application.serialize-check-status=WARN
dubbo.application.check-serializable=false
dubbo.tracing.enabled=true

dubbo.context.enable-thread-transmit=true
dubbo.context.enable-server-transmit=true

management.endpoint.metrics.enabled=true
management.endpoint.prometheus.enabled=true
management.prometheus.metrics.export.enabled=true

###########################elasticjob###################################
#elasticjob.enabled=true
#elasticjob.reg-center.server-lists=zookeeper.mugen-dev.svc.kiki.local:2181
#elasticjob.reg-center.namespace=kactivity/jobs
#elasticjob.reg-center.base-sleep-time-milliseconds=1000
#elasticjob.reg-center.max-sleep-time-milliseconds=3000
#elasticjob.reg-center.max-retries=5
#elasticjob.reg-center.session-timeout-milliseconds=10000
#elasticjob.reg-center.connection-timeout-milliseconds=10000

########################### tablestore ??????ak sk? ?oss????kiki?oss????? ??ak sk ###################################
kiki.ots.endpoint=https://MugenDev.ap-southeast-1.ots.aliyuncs.com
kiki.ots.instanceName=MugenDev

#spring ??????, ???????? ???????????
debug=true
#spring.cloud.nacos.config.enabled=false
server.port=8080
manage.grpc.port=30880

###########################redis###################################
spring.data.redis.host=mugen-dev-redis-01.evg.aliyun
spring.data.redis.password=w9c18KAOqc8A
spring.data.redis.port=6379
spring.data.redis.database=15
spring.data.redis.lettuce.pool.max-active=32

zipkin.host=jaeger-collector.quests-dev.svc.kiki.local

activity.quests.api.host=http://quests-gateway.quests-dev.svc.kiki.local:8080
activity.quests.app.key.suffix=mugen

# ????mse?????spring.zipkin.baseUrl??????? ?/api/v2/spans?
management.zipkin.tracing.endpoint=http://zipkin.quests-dev.svc.kiki.local:9411/api/v2/spans
management.tracing.sampling.probability=1.0
three.discord.bot-token.trex=MTM2NTI1OTQ4OTM2Nzk1MzQwOQ.Gk2PnL.L9CEGBbcWLP7lvMNjT7nnWMRUwlP1ZFNP-nDVs