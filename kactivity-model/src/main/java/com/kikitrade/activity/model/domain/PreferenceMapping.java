package com.kikitrade.activity.model.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 偏好映射数据结构
 * 用于存储在 progress_chest_config.extra_properties 中的偏好映射配置
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PreferenceMapping implements Serializable {

    /**
     * 偏好映射
     * key: 偏好类型 (如 "SELECTED_HERO")
     * value: 偏好值到奖励的映射 (如 "HERO_A" -> RewardConfig)
     */
    private Map<String, Map<String, RewardConfig>> preferenceMapping;

    /**
     * 奖励配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RewardConfig implements Serializable {
        
        /**
         * 道具ID (当 reward_type = ITEM 时使用)
         */
        private String itemId;
        
        /**
         * 礼包ID (当 reward_type = GIFT_PACK 时使用)
         */
        private String packId;
        
        /**
         * 数量 (当 reward_type = ITEM 时使用)
         */
        private Integer quantity;

        /**
         * 创建道具奖励配置
         */
        public static RewardConfig createItemReward(String itemId, Integer quantity) {
            RewardConfig config = new RewardConfig();
            config.setItemId(itemId);
            config.setQuantity(quantity);
            return config;
        }

        /**
         * 创建礼包奖励配置
         */
        public static RewardConfig createGiftPackReward(String packId) {
            RewardConfig config = new RewardConfig();
            config.setPackId(packId);
            return config;
        }

        /**
         * 判断是否为道具奖励
         */
        public boolean isItemReward() {
            return itemId != null && !itemId.trim().isEmpty();
        }

        /**
         * 判断是否为礼包奖励
         */
        public boolean isGiftPackReward() {
            return packId != null && !packId.trim().isEmpty();
        }
    }
}
