package com.kikitrade.activity.model.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 偏好映射数据结构
 * 用于存储在 progress_chest_config.extra_properties 中的偏好映射配置
 *
 * 根据优化.md文档，结构调整为数组格式，支持按顺序遍历偏好规则
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PreferenceMapping implements Serializable {

    /**
     * 偏好映射规则列表（按优先级顺序排列）
     * 系统会按数组顺序遍历，找到第一个匹配的规则后立即返回
     */
    private List<PreferenceRule> preferenceMapping;

    /**
     * 偏好规则
     * 每个规则包含一个偏好类型和对应的映射关系
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PreferenceRule implements Serializable {

        /**
         * 偏好类型 (如 "SELECTED_HERO", "SELECTED_FACTION")
         */
        private String preferenceType;

        /**
         * 偏好值到奖励的映射
         * key: 偏好值 (如 "HERO_A", "LIGHT")
         * value: 对应的奖励配置
         */
        private Map<String, RewardConfig> mapping;
    }

    /**
     * 奖励配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RewardConfig implements Serializable {

        /**
         * 奖励类型 (ITEM 或 GIFT_PACK)
         */
        private String rewardType;

        /**
         * 道具ID (当 reward_type = ITEM 时使用)
         */
        private String itemId;

        /**
         * 礼包ID (当 reward_type = GIFT_PACK 时使用)
         */
        private String packId;

        /**
         * 数量 (当 reward_type = ITEM 时使用)
         */
        private Integer quantity;

        /**
         * 创建道具奖励配置
         */
        public static RewardConfig createItemReward(String itemId, Integer quantity) {
            RewardConfig config = new RewardConfig();
            config.setRewardType("ITEM");
            config.setItemId(itemId);
            config.setQuantity(quantity);
            return config;
        }

        /**
         * 创建礼包奖励配置
         */
        public static RewardConfig createGiftPackReward(String packId) {
            RewardConfig config = new RewardConfig();
            config.setRewardType("GIFT_PACK");
            config.setPackId(packId);
            return config;
        }

        /**
         * 判断是否为道具奖励
         */
        public boolean isItemReward() {
            return "ITEM".equals(rewardType) && itemId != null && !itemId.trim().isEmpty();
        }

        /**
         * 判断是否为礼包奖励
         */
        public boolean isGiftPackReward() {
            return "GIFT_PACK".equals(rewardType) && packId != null && !packId.trim().isEmpty();
        }
    }
}
