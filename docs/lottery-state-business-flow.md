# 抽奖状态查询业务流程优化

## 概述

根据您的需求，我们已经优化了 `GET /api/lottery/state` 接口的核心处理逻辑，实现了基于用户偏好的动态宝箱过滤和排序功能。

## 阶段一：获取抽奖状态时的处理逻辑

### 1. 获取用户偏好
- 后端根据 `userId` 和 `prizePoolCode` 从 `user_preference` 表中查询用户当前的偏好值
- 例如：查询到用户选择了 `SELECTED_HERO`，其值为 `HERO_A_001`

### 2. 查询所有宝箱配置
- 后端从 `progress_chest_config` 表中，获取所有隶属于当前 `prizePoolCode` 的宝箱配置列表

### 3. 动态过滤（关键步骤）
后端在应用内存中，对上一步获取的列表进行过滤，只保留对当前用户有效的宝箱：

- **保留所有 `preference_type` 为 NULL 的通用宝箱**
- **保留所有 `preference_type` 为 `SELECTED_HERO` 且 `preference_value` 为 `HERO_A_001` 的英雄A专属宝箱**
- **丢弃所有 `preference_value` 为 `HERO_B_001` 的英雄B专属宝箱**

### 4. 排序
现在，后端对这个过滤后的列表，按照 `display_order` 字段进行升序排序：

- `display_order: 1` 的宝箱会排在最前面，成为"第一个"
- `display_order: 2` 的宝箱会排在第二位，成为"第二个"
- 以此类推

### 5. 计算状态并返回
最后，后端再对这个已排序的列表中的每一个宝箱，执行状态派生逻辑（查询 `user_claim_entitlement` 表），计算出每个宝箱的状态：

- **LOCKED**: 进度未达到解锁要求
- **UNCLAIMED**: 进度已达到且存在未领取的凭证
- **CLAIMED**: 进度已达到但无凭证（已被领取过）

然后将这个完整的、有序的、带状态的列表在 `chests` 数组中返回给前端。

## 阶段二：领取奖励时的处理逻辑

### 通过 claimId -> chest_id -> pack_id 链路查找奖励

1. **前端上送 claimId**: 当一个宝箱的状态为 `UNCLAIMED` 时，前端的 state 接口会返回一个与之对应的 `claimId`。用户点击"领取"按钮时，前端将这个 `claimId` 上送给后端。

2. **后端核销凭证**: 后端收到 `claimId` 后，在 `user_claim_entitlement` 表中找到这条唯一的领奖凭证记录。

3. **查找宝箱配置**: 从凭证记录中，后端可以获取到 `reward_source_id` 字段。对于进度宝箱来说，这个字段里存储的就是宝箱的唯一ID，即 `chest_id`。

4. **查找礼包ID**: 后端拿着这个 `chest_id`，去 `progress_chest_config` 表中查询，找到那条唯一的宝箱配置记录。从这条记录中，后端可以获取到 `pack_id_on_unlock` 字段，这个字段里存储的就是实际奖励内容对应的礼包ID。

5. **发放奖励**: 最后，后端拿着这个 `pack_id`，调用内部的"开启礼包"逻辑（即查询 `gift_pack_config` 和 `random_reward_pool` 表），解析出最终的奖励列表，并发放给用户。

6. **更新状态**: 将 `user_claim_entitlement` 表中该 `claimId` 对应的记录状态更新为 `CLAIMED`。

## 核心代码修改

### 1. buildChestInfo 方法优化

```java
private List<LotteryStateResponse.ChestInfo> buildChestInfo(String saasId, String userId, String prizePoolCode) {
    // 1. 获取用户偏好信息
    Map<String, String> userPreferences = userPreferenceBuilder.getUserPreferenceMap(userId, prizePoolCode);
    
    // 2. 查询所有宝箱配置
    List<ProgressChestConfig> allChestConfigs = progressChestConfigBuilder.findBySaasIdAndPrizePoolCode(saasId, prizePoolCode);
    
    // 3. 动态过滤：只保留对当前用户有效的宝箱
    List<ProgressChestConfig> filteredChestConfigs = allChestConfigs.stream()
        .filter(config -> isChestValidForUser(config, userPreferences))
        .collect(Collectors.toList());
    
    // 4. 按display_order排序（升序）
    filteredChestConfigs.sort((a, b) -> {
        Long orderA = a.getDisplayOrder() != null ? a.getDisplayOrder() : Long.MAX_VALUE;
        Long orderB = b.getDisplayOrder() != null ? b.getDisplayOrder() : Long.MAX_VALUE;
        return orderA.compareTo(orderB);
    });
    
    // 5. 计算状态并构建返回对象
    // ... 状态计算逻辑
}
```

### 2. 宝箱过滤逻辑

```java
private boolean isChestValidForUser(ProgressChestConfig chestConfig, Map<String, String> userPreferences) {
    // 通用宝箱：preference_type 为 NULL 或空字符串
    if (chestConfig.getPreferenceType() == null || chestConfig.getPreferenceType().trim().isEmpty()) {
        return true;
    }
    
    // 专属宝箱：需要检查用户偏好是否匹配
    String preferenceType = chestConfig.getPreferenceType();
    String preferenceValue = chestConfig.getPreferenceValue();
    String userPreferenceValue = userPreferences.get(preferenceType);
    
    return userPreferenceValue != null && userPreferenceValue.equals(preferenceValue);
}
```

### 3. 状态计算逻辑

```java
private String calculateChestState(ProgressChestConfig chestConfig, UserClaimEntitlement entitlement, long currentProgress) {
    // 存在未领取凭证
    if (entitlement != null) {
        return ActivityConstant.EntitlementStatusEnum.UNCLAIMED.name();
    }
    
    // 检查是否已解锁
    boolean isUnlocked = currentProgress >= chestConfig.getUnlockProgress();
    
    if (isUnlocked) {
        // 已解锁但无凭证，说明已被领取过
        return ActivityConstant.EntitlementStatusEnum.CLAIMED.name();
    } else {
        // 未解锁
        return ActivityConstant.EntitlementStatusEnum.LOCKED.name();
    }
}
```

## 测试验证

我们创建了完整的单元测试来验证修改后的业务流程：

1. **用户偏好过滤测试**: 验证只返回通用宝箱和匹配用户偏好的专属宝箱
2. **排序逻辑测试**: 验证宝箱按 `display_order` 升序排列
3. **状态计算测试**: 验证不同情况下的宝箱状态计算正确性
4. **无偏好用户测试**: 验证没有设置偏好的用户只能看到通用宝箱

## 优势

1. **性能优化**: 通过内存过滤减少不必要的数据传输
2. **用户体验**: 用户只看到与自己相关的宝箱，界面更简洁
3. **可扩展性**: 支持多种偏好类型的扩展
4. **数据一致性**: 确保前端显示的宝箱顺序和状态准确无误

## 注意事项

1. 确保 `display_order` 字段在宝箱配置中正确设置
2. 用户偏好变更后，下次查询状态时会立即生效
3. 宝箱配置的 `preference_type` 和 `preference_value` 需要与用户偏好表中的数据保持一致
