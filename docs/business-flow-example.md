# 抽奖状态查询业务流程示例

## 场景描述

假设我们有一个英雄主题的抽奖活动，用户可以选择不同的英雄，每个英雄都有专属的宝箱奖励。

## 数据准备

### 1. 用户偏好设置
```json
{
  "userId": "user_001",
  "prizePoolCode": "HERO_LOTTERY_2024",
  "preferences": {
    "SELECTED_HERO": "HERO_A_001"
  }
}
```

### 2. 宝箱配置数据
```json
[
  {
    "chestId": "chest_001",
    "chestName": "新手宝箱",
    "preferenceType": null,
    "preferenceValue": null,
    "displayOrder": 1,
    "unlockProgress": 10,
    "packIdOnUnlock": "pack_newbie_001"
  },
  {
    "chestId": "chest_002", 
    "chestName": "英雄A专属宝箱",
    "preferenceType": "SELECTED_HERO",
    "preferenceValue": "HERO_A_001",
    "displayOrder": 2,
    "unlockProgress": 25,
    "packIdOnUnlock": "pack_hero_a_001"
  },
  {
    "chestId": "chest_003",
    "chestName": "英雄B专属宝箱", 
    "preferenceType": "SELECTED_HERO",
    "preferenceValue": "HERO_B_001",
    "displayOrder": 3,
    "unlockProgress": 25,
    "packIdOnUnlock": "pack_hero_b_001"
  },
  {
    "chestId": "chest_004",
    "chestName": "高级宝箱",
    "preferenceType": null,
    "preferenceValue": null,
    "displayOrder": 4,
    "unlockProgress": 50,
    "packIdOnUnlock": "pack_advanced_001"
  },
  {
    "chestId": "chest_005",
    "chestName": "英雄A终极宝箱",
    "preferenceType": "SELECTED_HERO", 
    "preferenceValue": "HERO_A_001",
    "displayOrder": 5,
    "unlockProgress": 100,
    "packIdOnUnlock": "pack_hero_a_ultimate"
  }
]
```

### 3. 用户进度
```json
{
  "userId": "user_001",
  "prizePoolCode": "HERO_LOTTERY_2024",
  "currentProgress": 30,
  "cycleStartTime": 1704067200000
}
```

## 业务流程执行

### 阶段一：GET /api/lottery/state 处理流程

#### 步骤1：获取用户偏好
```
用户偏好: {"SELECTED_HERO": "HERO_A_001"}
```

#### 步骤2：查询所有宝箱配置
```
查询到5个宝箱配置
```

#### 步骤3：动态过滤
根据用户偏好过滤宝箱：

**保留的宝箱：**
- `chest_001` - 通用宝箱（preferenceType = null）
- `chest_002` - 英雄A专属宝箱（匹配用户偏好）
- `chest_004` - 通用宝箱（preferenceType = null）
- `chest_005` - 英雄A专属宝箱（匹配用户偏好）

**过滤掉的宝箱：**
- `chest_003` - 英雄B专属宝箱（不匹配用户偏好）

#### 步骤4：排序
按 displayOrder 升序排序：
1. `chest_001` (displayOrder: 1)
2. `chest_002` (displayOrder: 2)
3. `chest_004` (displayOrder: 4)
4. `chest_005` (displayOrder: 5)

#### 步骤5：计算状态
根据用户当前进度30和领取凭证情况：

- `chest_001` (解锁进度10): 已解锁，状态 = CLAIMED（假设已领取过）
- `chest_002` (解锁进度25): 已解锁，状态 = UNCLAIMED（假设有未领取凭证）
- `chest_004` (解锁进度50): 未解锁，状态 = LOCKED
- `chest_005` (解锁进度100): 未解锁，状态 = LOCKED

#### 最终返回结果
```json
{
  "success": true,
  "data": {
    "chests": [
      {
        "chestId": "chest_001",
        "chestName": "新手宝箱",
        "unlockProgress": 10,
        "state": "CLAIMED",
        "claimId": null
      },
      {
        "chestId": "chest_002", 
        "chestName": "英雄A专属宝箱",
        "unlockProgress": 25,
        "state": "UNCLAIMED",
        "claimId": "claim_12345"
      },
      {
        "chestId": "chest_004",
        "chestName": "高级宝箱", 
        "unlockProgress": 50,
        "state": "LOCKED",
        "claimId": null
      },
      {
        "chestId": "chest_005",
        "chestName": "英雄A终极宝箱",
        "unlockProgress": 100,
        "state": "LOCKED", 
        "claimId": null
      }
    ]
  }
}
```

### 阶段二：POST /api/rewards/claim 处理流程

当用户点击领取 `chest_002` 时：

#### 步骤1：前端上送 claimId
```json
{
  "userId": "user_001",
  "claimId": "claim_12345"
}
```

#### 步骤2：后端核销凭证
从 `user_claim_entitlement` 表查询：
```json
{
  "claimId": "claim_12345",
  "userId": "user_001", 
  "rewardType": "PROGRESS_CHEST",
  "rewardSourceId": "chest_002",
  "status": "UNCLAIMED"
}
```

#### 步骤3：查找宝箱配置
通过 `rewardSourceId` = "chest_002" 查询宝箱配置：
```json
{
  "chestId": "chest_002",
  "packIdOnUnlock": "pack_hero_a_001"
}
```

#### 步骤4：查找礼包配置并发放奖励
通过 `packIdOnUnlock` = "pack_hero_a_001" 查询礼包配置并发放奖励：
```json
{
  "packId": "pack_hero_a_001",
  "rewards": [
    {"itemId": "gold", "quantity": 500},
    {"itemId": "hero_a_fragment", "quantity": 10}
  ]
}
```

#### 步骤5：更新凭证状态
将凭证状态更新为 `CLAIMED`

#### 最终返回结果
```json
{
  "success": true,
  "claimId": "claim_12345",
  "rewards": [
    {
      "itemId": "gold",
      "itemName": "金币", 
      "quantity": 500
    },
    {
      "itemId": "hero_a_fragment",
      "itemName": "英雄A碎片",
      "quantity": 10
    }
  ]
}
```

## 关键优势

1. **个性化体验**: 用户只看到与自己选择相关的宝箱
2. **有序展示**: 宝箱按照配置的显示顺序排列
3. **准确状态**: 实时计算每个宝箱的解锁和领取状态
4. **清晰链路**: claimId -> chestId -> packId 的奖励发放链路清晰可追踪

## 扩展性

这个设计支持：
- 多种偏好类型（英雄选择、VIP等级、地区偏好等）
- 复杂的宝箱解锁条件
- 灵活的奖励配置
- 动态的用户偏好变更
