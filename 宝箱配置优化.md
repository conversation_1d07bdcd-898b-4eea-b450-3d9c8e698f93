总结背景为了解决 progress_chest_config 表中为最后三个宝箱配置 27 条记录（3 宝箱 × 9 种偏好值）导致配置臃肿的问题，采用了 方案 3：使用 extra_properties 字段存储偏好映射，并新增 reward_type 字段支持直接发放道具（ITEM）或礼包（GIFT_PACK）。此外，针对 item_master_config 表缺少发放数量的问题，进一步在 progress_chest_config 表中新增 quantity 字段，以支持 reward_type = ITEM 时直接指定道具数量，减少 gift_pack_config 配置量。以下总结改动细节，供后续代码优化使用。改动总结1. 设计目标

- 减少配置量：将 progress_chest_config 配置从 27 条（3 宝箱 × 9 偏好）降至 3 条（每宝箱 1 条）。
- 支持道具数量：为 reward_type = ITEM 的场景直接指定发放数量，避免依赖 gift_pack_config。
- 集中管理：将偏好映射和数量规则集中在 progress_chest_config，简化维护。
- 兼容性：保持与 Nacos 监听、OTS 存储、多租户场景的兼容性。
- 表结构改动在 progress_chest_config 表中新增以下字段：

- reward_type：
  - 类型：String。
  - 枚举值：ITEM（直接发放道具，引用 item_master_config.item_id），GIFT_PACK（发放礼包，引用 gift_pack_config.pack_id）。
  - 用途：区分奖励类型，决定 pack_id_on_unlock 的引用目标。
- quantity：
  数量 ：
  - 类型：INTEGER。
  - 用途：reward_type = ITEM 时指定道具发放数量；reward_type = GIFT_PACK 时忽略（可为 null）。
  - 默认值：null（兼容旧数据）。

其他字段（如 saas_id, prize_pool_code, chest_id, pack_id_on_unlock, unlock_progress, progress_type, extra_properties 等）保持不变。
其他字段（如 saas_id， prize_pool_code， chest_id， pack_id_on_unlock， unlock_progress， progress_type， extra_properties 等）保持不变。3. JSON 格式更新 tenant1_config.json 中 progress_chest_config 部分的格式，支持偏好映射和数量配置：

json



```json
{
  "saas_id": "tenant1",
  "progress_chest_config": [
    {
      "saas_id": "tenant1",
      "prize_pool_code": "ADVANCED_POOL",
      "chest_id": "chest_004",
      "reward_type": "ITEM",
      "pack_id_on_unlock": "gold",
      "quantity": 100, // 默认发放 100 个金币
      "unlock_progress": 5000,
      "is_active": true,
      "extra_properties": {
        "preference_mapping": {
          "SELECTED_HERO": {
            "HERO_A": { "item_id": "gold", "quantity": 100 },
            "HERO_B": { "item_id": "exp_potion", "quantity": 50 },
            "HERO_C": { "item_id": "gem", "quantity": 20 },
            // ... 9种偏好值
          }
        }
      },
      "scene_code": "ADVANCED_PROGRESS",
      "chest_name": "Advanced Item Chest",
      "display_order": 1,
      "description": "Item chest based on hero preference",
      "icon_url": "https://example.com/chest_icon4.png",
      "valid_start_time": 1696600000000,
      "valid_end_time": 1704067200000,
      "chest_value": 100,
      "max_claim_per_user": 1,
      "reset_cycle_days": 0,
      "priority": 1,
      "create_time": 1696600000000,
      "update_time": 1696600000000,
      "creator": "admin"
    },
    {
      "saas_id": "tenant1",
      "prize_pool_code": "ADVANCED_POOL",
      "chest_id": "chest_005",
      "reward_type": "GIFT_PACK",
      "pack_id_on_unlock": "pack_005",
      "quantity": null, // GIFT_PACK 时忽略
      "unlock_progress": 10000,
      "progress_type": "POINTS_EARNED",
      "is_active": true,
      "extra_properties": {
        "preference_mapping": {
          "SELECTED_HERO": {
            "HERO_A": { "pack_id": "pack_005_A" },
            "HERO_B": { "pack_id": "pack_005_B" },
            "HERO_C": { "pack_id": "pack_005_C" },
            // ... 9种偏好值
          }
        }
      }
      // 其他字段类似
    }
    // chest_006 类似
  ],
  "gift_pack_config": [
    {
      "saas_id": "tenant1",
      "pack_id": "pack_005_A",
      "rule_id": "rule_001",
      "rule_type": "FIXED_ITEM",
      "item_id": "gold",
      "quantity_min": 100,
      "quantity_max": 100
      // ...
    }
    // ... 9条记录 for pack_005 (if reward_type = GIFT_PACK)
  ],
  "item_master_config": [
    {
      "saas_id": "tenant1",
      "item_id": "gold",
      "external_item_id": "GOLD_001",
      "item_name": "Gold Coin",
      "item_type": "ITEM"
      // ...
    }
    // ... 其他道具
  ]
}
```

格式说明

- 每宝箱 1 条记录：progress_chest_config 共 3 条记录（chest_004, chest_005, chest_006）。
  每个宝箱 1 条记录：progress_chest_config 3 条记录（chest_004、chest_005、chest_006）。
- reward_type：
  - ITEM：pack_id_on_unlock 指向 item_master_config.item_id，quantity 指定默认数量。
  - GIFT_PACK：pack_id_on_unlock 指向 gift_pack_config.pack_id，quantity 为 null。
- extra_properties：
  - 存储 preference_mapping，格式：
    - reward_type = ITEM：{ "item_id": "xxx", "quantity": xxx }。
      reward_type = ITEM：{ “item_id”： “xxx”， “quantity”： xxx }。
    - reward_type = GIFT_PACK：{ "pack_id": "xxx" }。
      reward_type = GIFT_PACK：{ “pack_id”： “xxx” }。
  - 示例：SELECTED_HERO:HERO_A 映射到 { "item_id": "gold", "quantity": 100 } 或 { "pack_id": "pack_005_A" }。
    示例：SELECTED_HERO：HERO_A 映射到 { “item_id”： “gold”， “quantity”： 100 } 或 { “pack_id”： “pack_005_A” }。
- 配置量：
  - 场景 1：全部偏好奖励为道具：
    - progress_chest_config：3 条。
    - gift_pack_config：0 条。
    - item_master_config：9 条（假设已有）。
    - 总计：3 条（加现有道具配置）。
  - 场景 2：部分偏好奖励为道具（6 道具 + 3 礼包 per 宝箱）：
    - progress_chest_config：3 条。
    - gift_pack_config：9 条（3 礼包 × 3 偏好）。
    - item_master_config：6 条（假设已有）。
    - 总計：12 條（加現有道具配置）。
  - 场景 3：全部偏好奖励为礼包：
    - progress_chest_config：3 条。
    - gift_pack_config：27 条（3 礼包 × 9 偏好）。
    - 总计：30 条。
- JSON Schema
- JSON 架构更新 progress_chest_config 的 JSON Schema，支持 reward_type 和 quantity：

5. 逻辑改动要点

- 插入逻辑：
  - 更新 ConfigSyncService，在 createRowChange 方法中添加 quantity 字段的处理。
  - 校验 reward_type 和 pack_id_on_unlock：
    - ITEM：确保 pack_id_on_unlock 存在于 item_master_config.item_id，quantity 为正整数。
    - GIFT_PACK：确保 pack_id_on_unlock 存在于 gift_pack_config.pack_id，quantity 可为 null。
  - 校验 extra_properties.preference_mapping：
    - ITEM：映射到 { item_id, quantity }，确保 item_id 有效，quantity 为正整数。
    - GIFT_PACK：映射到 { pack_id }，确保 pack_id 有效。
- 奖励发放逻辑：
  - 查询 progress_chest_config，获取 reward_type, pack_id_on_unlock, quantity。
    查询 progress_chest_config，获取 reward_type， pack_id_on_unlock， quantity。
  - 解析 extra_properties.preference_mapping，根据用户偏好（SELECTED_HERO:HERO_X）获取目标 item_id/pack_id 和 quantity（ITEM 时）。
    解析 extra_properties.preference_mapping，根据用户偏好（SELECTED_HERO：HERO_X）获取目标 item_id/pack_id 和 quantity（ITEM 时）。
  - 分支处理：
    - ITEM：发放指定数量的道具（issueItem(userId, saasId, itemId, quantity)）。
      ITEM： 发放指定数量的道具（issueItem（userId， saasId， itemId， quantity））。
    - GIFT_PACK：发放礼包（issueGiftPack(userId, saasId, packId)）。
      GIFT_PACK：发放礼包（issueGiftPack（userId， saasId， packId））。
- 缓存：
  缓存 ：
  - 缓存 progress_chest_config 和解析后的 preference_mapping 到 Redis，减少 OTS 查询和 JSON 解析开销。
- 错误处理：
  - 校验失败记录到阿里云 SLS。
  - 批量插入 OTS，每批 100 条记录，支持重试。
- 配置量优化

- 原设计：27 条 progress_chest_config + 27 条 gift_pack_config = 54 条。
- 新设计：
  - 场景 1：全部偏好奖励为道具：
    - progress_chest_config：3 条。
    - gift_pack_config：0 条。
    - 总计：3 条（加现有 item_master_config）。
  - 场景 2：部分偏好奖励为道具（6 道具 + 3 礼包 per 宝箱）：
    - progress_chest_config：3 条。
    - gift_pack_config：9 条。
    - 总计：12 条（加现有 item_master_config）。
  - 场景 3：全部偏好奖励为礼包：
    - progress_chest_config：3 条。
    - gift_pack_config：27 条。
    - 总计：30 条。
- 结论：新增 quantity 字段显著减少 gift_pack_config 配置（0-9 条 vs 27 条），尤其当偏好奖励多为道具时。
- 优点

- 减少配置量：progress_chest_config 降至 3 条，gift_pack_config 可减至 0-9 条。
- 集中管理：偏好映射和数量规则集中在 extra_properties 和 quantity 字段。
- 灵活性：支持道具（带数量）和礼包奖励，适应多种场景。
- 低开发成本：仅新增两个字段，逻辑调整较小。
- 兼容性：符合 Nacos 监听、OTS 存储、多租户隔离。
- 注意事项

- 表结构更新：需在 OTS 中为 progress_chest_config 添加 reward_type 和 quantity 字段，兼容旧数据（quantity 默认 null）。
- 校验：
  检查：
  - 确保 reward_type 为 ITEM 时，quantity 和 preference_mapping 中的 quantity 为正整数。
  - 验证 pack_id_on_unlock 和 preference_mapping 中的 item_id/pack_id 引用有效。
- 性能：
  - 缓存 preference_mapping 到 Redis，降低 JSON 解析开销。
  - 批量插入 OTS，限制每批 100 条记录。
- 测试：
  - 验证 3 种场景（全道具、混合、 全礼包）的配置和发放逻辑。
  - 测试 JSON 解析性能（例如，100 个宝箱，9 种偏好）。
- 下一步

- 提供 random_reward_pool 或其他表的 JSON 格式。
- 确认偏好奖励类型比例（道具 vs 礼包）。
- 优化 ConfigSyncService 和奖励发放代码（交给 Claude 优化）。
- 测试大批量配置（例如，100 个宝箱）。

------

备注：此文档总结了 progress_chest_config 表的改动，包括新增字段、JSON 格式、逻辑要点和配置量优化。请提供给 Claude 用于代码优化，确保实现插入、校验和奖励发放逻辑，并考虑缓存和性能优化。若需进一步补充（如其他表格式或测试用例），请告知！