# 奖励中台后端技术规格书 (OTS最终版)

## 1. 系统概述

本文档为一份完整的后端技术规格说明书，旨在为开发一个功能全面、高内聚、低耦合的**奖励中台**提供清晰、详尽的实现指南。该系统**专为阿里云表格存储（OTS）设计**，作为平台能力，支持由多个外部业务渠道驱动，并集成了动态奖池、多维度风控、多种激励机制和灵活的概率策略。

### 核心功能：

- **概率性抽奖**：
  - **动态奖池**：支持根据用户存储的、与奖池挂钩的偏好（如选择的英雄），动态替换奖池中的专属奖品。
  - **双重概率策略**：支持“整体概率 (OVERALL)”和“单品概率 (SINGLE)”两种模式。
  - **两阶段抽奖**：提供独立的“资产兑换抽奖券”和“消耗抽奖券批量抽奖”接口，由前端编排调用。
- **组合奖励 (礼包/宝箱)**：支持配置“礼包”型奖品，内含多个固定奖励和随机奖励。可通过抽奖获得，也可由外部服务直接按ID触发发放。
- **统一领奖能力**：提供一个统一、抽象的领奖接口，用于核销由不同业务系统（如抽奖进度、任务系统）为用户生成的“领奖凭证”。
- **定向奖励发放**：提供安全的内部API，允许其他微服务直接指定奖励内容并调用本中台进行发放。
- **多维度抽奖风控**：可按日、周、月三个维度精确控制用户的抽奖次数上限。
- **后台管理支持**：提供独立的API接口，供管理后台配置活动和监控库存。

## 2. 系统架构与流程图

(保持不变)

## 3. 数据模型 (OTS 表设计)

**注意**: 所有与租户相关的配置表，主键均以 `saas_id` 作为**分区键**，以确保数据隔离和性能。

### 3.1 `item_master_config` - 物品主数据表

(保持不变)

### 3.2 `prize_pool` - 奖池配置表

(保持不变)

### 3.3 `prize_config` - 奖品配置表

(保持不变)

### 3.4 `user_lottery_profile` - 用户抽奖档案表

(保持不变)

### 3.5 `progress_chest_config` - 进度宝箱配置表

| **字段**            | **类型**                               | **含义**                                    |
| ------------------- | -------------------------------------- | ------------------------------------------- |
| `saas_id`           | STRING                                 | **主键1 (分区键)**: 对接的应用/租户ID       |
| `prize_pool_code`   | STRING                                 | **主键2**: 关联的奖池编码                   |
| `chest_id`          | STRING                                 | **主键3**: 宝箱配置的唯一ID (UUID)          |
| `reward_type`       | STRING                                 | **默认**奖励类型 (`ITEM` 或 `GIFT_PACK`)    |
| `pack_id_on_unlock` | STRING                                 | **默认**奖励的道具/礼包ID                   |
| `quantity`          | BIGINT                                 | **默认**奖励的数量 (当`reward_type`='ITEM') |
| `extra_properties`  | STRING (JSON)                          | **扩展属性，包含优先的偏好映射规则**        |
| `unlock_progress`   | BIGINT                                 | 解锁所需的进度值                            |
| `scene_code`        | STRING                                 | 场景编码 (用于前端展示定位)                 |
| `chest_name`        | STRING                                 | 宝箱名称                                    |
| `display_order`     | BIGINT                                 | 显示顺序                                    |
| `is_active`         | BOOLEAN                                | 是否启用                                    |
| `create_time`       | BIGINT (Timestamp)                     | 创建时间                                    |
| `update_time`       | BIGINT (Timestamp)                     | 更新时间                                    |
| **PK**              | `(saas_id, prize_pool_code, chest_id)` | 复合主键                                    |

**`extra_properties` JSON 结构示例 (核心变更)**:

```
{
  "preference_mapping": [
    {
      "preference_type": "SELECTED_HERO",
      "mapping": {
        "HERO_A": { "reward_type": "ITEM", "item_id": "item_A", "quantity": 1 },
        "HERO_B": { "reward_type": "GIFT_PACK", "pack_id": "pack_B" }
      }
    },
    {
      "preference_type": "SELECTED_FACTION",
      "mapping": {
        "LIGHT": { "reward_type": "ITEM", "item_id": "item_C", "quantity": 1 }
      }
    }
  ]
}
```

### 3.6 `user_claim_entitlement` - 用户领奖凭证表

(保持不变)

### 3.7 `user_preference` - 用户偏好表

(保持不变)

### 3.8 `draw_history` - 抽奖历史记录表

(保持不变)

### 3.9 `gift_pack_config` - 礼包内容规则表

(保持不变)

### 3.10 `random_reward_pool` - 随机奖励池定义表

(保持不变)

### 3.11 `direct_reward_issuance_log` - 定向奖励发放流水表

(保持不变)

### 3.12 `user_progress_tracker` - 用户进度跟踪表

(保持不变)

## 4. 核心业务逻辑

### 4.1 奖励发放逻辑 (已重构)

当用户领取一个进度宝箱 (`/api/rewards/claim`) 或由系统直接发放奖励时，确定最终奖励的逻辑如下：

根据claim从user_claim_entitlement表获取到reward_source_id，也就是chest_id，查找progress_chest_config表获取到prize_pool_code

1. **获取用户偏好**: 根据 `userId` 和 `prizePoolCode` 查询 `user_preference` 表，获取用户在此奖池下的所有偏好设置，存入一个Map。

   - 示例: `Map<String, String> userPrefs = {"SELECTED_HERO": "HERO_A", "SELECTED_FACTION": "DARK"}`

2. **获取宝箱配置**: 获取 `progress_chest_config` 的完整配置，包括 `extra_properties`。

3. **遍历优先规则列表**:

   - **按顺序**遍历 `extra_properties.preference_mapping` 数组。

4. **执行匹配**:

   - 对于数组中的每一个规则对象：

     a.  获取其 preference_type (例如 "SELECTED_HERO")。

     b.  检查 userPrefs 中是否存在该 preference_type。

     c.  如果存在，获取用户的偏好值 (例如 "HERO_A")。

     d.  在该规则的 mapping 对象中，查找是否存在与用户偏好值匹配的键。

     e.  如果找到匹配，则将该键对应的值（例如 { "reward_type": "ITEM", "item_id": "item_A", "quantity": 1 }）作为最终奖励，并立即中断遍历。

5. **处理默认情况**:

   - 如果遍历完整个数组都没有找到任何匹配项，系统则会**回退**，使用 `progress_chest_config` 表顶层定义的**默认奖励**（`reward_type`, `pack_id_on_unlock`, `quantity`）。

6. **执行发放**:

   - 根据最终确定的奖励（无论是来自偏好匹配还是默认），执行相应的发放策略（`issueItem` 或 `issueGiftPack`）。

### 4.2 批量抽奖与进度处理

(保持不变)

### 4.3 缓存策略

(保持不变)

## 5. API 接口设计

(保持不变)