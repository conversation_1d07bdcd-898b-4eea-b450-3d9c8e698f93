# 奖励中台后端技术规格书 (OTS最终版)

## 1. 系统概述

本文档为一份完整的后端技术规格说明书，旨在为开发一个功能全面、高内聚、低耦合的**奖励中台**提供清晰、详尽的实现指南。该系统**专为阿里云表格存储（OTS）设计**，作为平台能力，支持由多个外部业务渠道驱动，并集成了动态奖池、多维度风控、多种激励机制和灵活的概率策略。

### 核心功能：

- **概率性抽奖**：
  - **动态奖池**：支持根据用户存储的、与奖池挂钩的偏好（如选择的英雄），动态替换奖池中的专属奖品。
  - **双重概率策略**：支持“整体概率 (OVERALL)”和“单品概率 (SINGLE)”两种模式。
  - **两阶段抽奖**：提供独立的“资产兑换抽奖券”和“消耗抽奖券批量抽奖”接口，由前端编排调用。
- **组合奖励 (礼包/宝箱)**：支持配置“礼包”型奖品，内含多个固定奖励和随机奖励。可通过抽奖获得，也可由外部服务直接按ID触发发放。
- **统一领奖能力**：提供一个统一、抽象的领奖接口，用于核销由不同业务系统（如抽奖进度、任务系统）为用户生成的“领奖凭证”。
- **定向奖励发放**：提供安全的内部API，允许其他微服务直接指定奖励内容并调用本中台进行发放。
- **多维度抽奖风控**：可按日、周、月三个维度精确控制用户的抽奖次数上限。
- **后台管理支持**：提供独立的API接口，供管理后台配置活动和监控库存。

## 2. 系统架构与流程图

(保持不变)

## 3. 数据模型 (OTS 表设计)

**注意**: 所有与租户相关的配置表，主键均以 `saas_id` 作为**分区键**，以确保数据隔离和性能。

### 3.1 `item_master_config` - 物品主数据表

(保持不变)

### 3.2 `prize_pool` - 奖池配置表 (已更新)

| **字段**                   | **类型**           | **含义**                                     |
| -------------------------- | ------------------ | -------------------------------------------- |
| `saas_id`                  | STRING             | **主键1 (分区键)**: 对接的应用/租户ID        |
| `code`                     | STRING             | **主键2**: 奖池唯一编码                      |
| `name`                     | STRING             | 奖池名称                                     |
| `exchange_rules`           | STRING (JSON)      | 兑换规则                                     |
| `probability_strategy`     | STRING             | 概率策略 (`OVERALL` 或 `SINGLE`)             |
| `fallback_prize_config_id` | STRING             | 兜底奖品配置ID (`SINGLE`策略专用)            |
| `daily_limit`              | BIGINT             | 每日抽奖上限 (-1表示无限制)                  |
| `weekly_limit`             | BIGINT             | 每周抽奖上限 (-1表示无限制)                  |
| `monthly_limit`            | BIGINT             | 每月抽奖上限 (-1表示无限制)                  |
| `chest_cycle_days`         | BIGINT             | 进度宝箱的刷新周期（天）                     |
| `chest_cycle_start_time`   | BIGINT (Timestamp) | **进度宝箱的第一个周期的开始时间（起锚点）** |
| `status`                   | STRING             | 状态 (`ACTIVE`, `INACTIVE`)                  |
| `start_time`               | BIGINT (Timestamp) | 活动开始时间                                 |
| `end_time`                 | BIGINT (Timestamp) | 活动结束时间                                 |
| `create_time`              | BIGINT (Timestamp) | 创建时间                                     |
| `update_time`              | BIGINT (Timestamp) | 更新时间                                     |
| **PK**                     | `(saas_id, code)`  | 复合主键                                     |

### 3.3 `prize_config` - 奖品配置表

(保持不变)

### 3.4 `user_lottery_profile` - 用户抽奖档案表

(保持不变)

### 3.5 `progress_chest_config` - 进度宝箱配置表

(保持不变)

### 3.6 `user_claim_entitlement` - 用户领奖凭证表

(保持不变)

### 3.7 `user_preference` - 用户偏好表

(保持不变)

### 3.8 `draw_history` - 抽奖历史记录表

(保持不变)

### 3.9 `gift_pack_config` - 礼包内容规则表

(保持不变)

### 3.10 `random_reward_pool` - 随机奖励池定义表

(保持不变)

### 3.11 `direct_reward_issuance_log` - 定向奖励发放流水表

(保持不变)

### 3.12 `user_progress_tracker` - 用户进度跟踪表

(保持不变)

## 4. 核心业务逻辑

### 4.1 奖励发放逻辑 (已重构)

(保持不变)

### 4.2 批量抽奖与进度处理 (已更新)

1. 抽奖主流程：

   (保持不变)

2. **更新用户进度 (新逻辑)**：

   - 在批量抽奖成功后，读取奖池配置中的 `chest_cycle_days` 和 `chest_cycle_start_time` (活动周期起锚点)。
   - 查询 `user_progress_tracker` 表中该用户和奖池的记录。
   - **计算当前所属周期**: 根据 `chest_cycle_start_time`、`chest_cycle_days` 和当前服务器时间，计算出用户当前所处周期的**理论开始时间 `current_cycle_start_time`**。
     - **公式**: `current_cycle_start_time = chest_cycle_start_time + floor((now - chest_cycle_start_time) / (chest_cycle_days * 24 * 3600)) * (chest_cycle_days * 24 * 3600)`
   - **判断周期是否已变更**: 比较计算出的 `current_cycle_start_time` 和 `user_progress_tracker` 表中记录的 `cycle_start_time`。
     - **如果已变更 (进入新周期)**：将 `current_progress` 重置为本次的抽奖次数，并更新 `user_progress_tracker` 中的 `cycle_start_time` 为 `current_cycle_start_time`。
     - **如果未变更 (仍在上个周期)**：将 `current_progress` 累加上本次的抽奖次数。
   - 将更新后的进度写回 `user_progress_tracker` 表。

3. 检查并生成凭证：

   (保持不变)

### 4.3 缓存策略

(保持不变)

## 5. API 接口设计

(保持不变)